# PowerShell script to start Financial Report servers
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "  Financial Report Application" -ForegroundColor Cyan
Write-Host "  Starting Backend and Frontend Servers" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check if Node.js is installed
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js version: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Node.js from https://nodejs.org/" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if npm is available
try {
    $npmVersion = npm --version
    Write-Host "✅ NPM version: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ NPM is not available" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""

# Check and install dependencies
if (-not (Test-Path "node_modules")) {
    Write-Host "📦 Installing frontend dependencies..." -ForegroundColor Yellow
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Failed to install frontend dependencies" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
    Write-Host "✅ Frontend dependencies installed" -ForegroundColor Green
} else {
    Write-Host "✅ Frontend dependencies already installed" -ForegroundColor Green
}

if (-not (Test-Path "server\node_modules")) {
    Write-Host "📦 Installing backend dependencies..." -ForegroundColor Yellow
    Set-Location server
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Failed to install backend dependencies" -ForegroundColor Red
        Set-Location ..
        Read-Host "Press Enter to exit"
        exit 1
    }
    Set-Location ..
    Write-Host "✅ Backend dependencies installed" -ForegroundColor Green
} else {
    Write-Host "✅ Backend dependencies already installed" -ForegroundColor Green
}

# Check database
if (-not (Test-Path "server\database\finreport.db")) {
    Write-Host "🗄️ Initializing database..." -ForegroundColor Yellow
    Set-Location server
    npm run migrate
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Database migration failed" -ForegroundColor Red
        Set-Location ..
        Read-Host "Press Enter to exit"
        exit 1
    }
    Set-Location ..
    Write-Host "✅ Database initialized" -ForegroundColor Green
} else {
    Write-Host "✅ Database already exists" -ForegroundColor Green
}

Write-Host ""

# Kill existing processes
Write-Host "🔄 Stopping existing servers..." -ForegroundColor Yellow
Get-Process | Where-Object {$_.ProcessName -eq "node"} | ForEach-Object {
    $port = netstat -ano | Select-String ":3000|:5000" | Select-String $_.Id
    if ($port) {
        Write-Host "Stopping process $($_.Id) on port 3000/5000" -ForegroundColor Yellow
        Stop-Process -Id $_.Id -Force -ErrorAction SilentlyContinue
    }
}

Write-Host ""
Write-Host "🚀 Starting servers..." -ForegroundColor Cyan
Write-Host ""
Write-Host "📍 Backend Server: http://localhost:5000" -ForegroundColor Green
Write-Host "📍 Frontend Server: http://localhost:3000" -ForegroundColor Green
Write-Host ""

# Try to start with concurrently first
try {
    Write-Host "Starting with concurrently..." -ForegroundColor Yellow
    npm run start:dev
} catch {
    Write-Host "⚠️ Concurrently failed, starting manually..." -ForegroundColor Yellow
    
    # Start backend in new window
    Write-Host "🔧 Starting backend server..." -ForegroundColor Yellow
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd server; npm start" -WindowStyle Normal
    
    # Wait for backend
    Start-Sleep -Seconds 5
    
    # Start frontend in new window
    Write-Host "🔧 Starting frontend server..." -ForegroundColor Yellow
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "npm start" -WindowStyle Normal
    
    Write-Host ""
    Write-Host "✅ Servers started in separate windows" -ForegroundColor Green
    Write-Host "Close the PowerShell windows to stop the servers" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "  Server startup completed" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Read-Host "Press Enter to exit"
