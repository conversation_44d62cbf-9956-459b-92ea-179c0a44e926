const BaseModel = require('./BaseModel');

class SalaryModel extends BaseModel {
  constructor() {
    super('salary');
  }

  getSearchableFields() {
    return ['name', 'payrollMonth', 'salaryMaster'];
  }

  getDefaultOrderBy() {
    return 'createdAt DESC';
  }

  // Find by employee and payroll month
  findByEmployeeAndMonth(name, payrollMonth) {
    return new Promise((resolve, reject) => {
      const query = `SELECT * FROM ${this.tableName} WHERE name = ? AND payrollMonth = ?`;
      this.db.get(query, [name, payrollMonth], (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row || null);
        }
      });
    });
  }

  // Get unique values for filters
  getUniqueMonths() {
    return new Promise((resolve, reject) => {
      const query = `SELECT DISTINCT payrollMonth FROM ${this.tableName} WHERE payrollMonth IS NOT NULL AND payrollMonth != '' ORDER BY payrollMonth`;
      this.db.all(query, [], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve((rows || []).map(row => row.payrollMonth));
        }
      });
    });
  }

  getUniqueEmployees() {
    return new Promise((resolve, reject) => {
      const query = `SELECT DISTINCT name FROM ${this.tableName} WHERE name IS NOT NULL AND name != '' ORDER BY name`;
      this.db.all(query, [], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve((rows || []).map(row => row.name));
        }
      });
    });
  }

  getUniqueSalaryMasters() {
    return new Promise((resolve, reject) => {
      const query = `SELECT DISTINCT salaryMaster FROM ${this.tableName} WHERE salaryMaster IS NOT NULL AND salaryMaster != '' ORDER BY salaryMaster`;
      this.db.all(query, [], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve((rows || []).map(row => row.salaryMaster));
        }
      });
    });
  }

  // Bulk upsert for Excel imports
  bulkUpsert(dataArray) {
    return new Promise((resolve, reject) => {
      if (!Array.isArray(dataArray) || dataArray.length === 0) {
        resolve({ imported: 0, updated: 0 });
        return;
      }

      this.db.serialize(() => {
        this.db.run('BEGIN TRANSACTION');

        let imported = 0;
        let updated = 0;
        let completed = 0;
        let hasError = false;

        const processItem = (item, index) => {
          const timestamp = new Date().toISOString();

          // Check if record exists
          const selectQuery = `SELECT id FROM ${this.tableName} WHERE name = ? AND payrollMonth = ?`;
          this.db.get(selectQuery, [item.name, item.payrollMonth], (err, existing) => {
            if (err) {
              if (!hasError) {
                hasError = true;
                this.db.run('ROLLBACK', () => {
                  reject(err);
                });
              }
              return;
            }

            if (existing) {
              // Update existing record
              const updateQuery = `
                UPDATE ${this.tableName}
                SET gross = ?, pf = ?, salaryMaster = ?, updatedAt = ?, source = ?
                WHERE id = ?
              `;
              this.db.run(updateQuery, [
                item.gross,
                item.pf || 0,
                item.salaryMaster || null,
                timestamp,
                item.source || 'import-update',
                existing.id
              ], (updateErr) => {
                if (updateErr) {
                  if (!hasError) {
                    hasError = true;
                    this.db.run('ROLLBACK', () => {
                      reject(updateErr);
                    });
                  }
                  return;
                }
                updated++;
                completed++;
                checkCompletion();
              });
            } else {
              // Insert new record
              const insertQuery = `
                INSERT INTO ${this.tableName} (payrollMonth, name, gross, pf, salaryMaster, createdAt, updatedAt, source)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
              `;
              this.db.run(insertQuery, [
                item.payrollMonth,
                item.name,
                item.gross,
                item.pf || 0,
                item.salaryMaster || null,
                timestamp,
                timestamp,
                item.source || 'import-new'
              ], (insertErr) => {
                if (insertErr) {
                  if (!hasError) {
                    hasError = true;
                    this.db.run('ROLLBACK', () => {
                      reject(insertErr);
                    });
                  }
                  return;
                }
                imported++;
                completed++;
                checkCompletion();
              });
            }
          });
        };

        const checkCompletion = () => {
          if (completed === dataArray.length && !hasError) {
            this.db.run('COMMIT', (commitErr) => {
              if (commitErr) {
                reject(commitErr);
              } else {
                resolve({ imported, updated });
              }
            });
          }
        };

        // Process all items
        dataArray.forEach(processItem);
      });
    });
  }

  // Get employees for discrepancy analysis
  getEmployeesByMonth(payrollMonth) {
    return new Promise((resolve, reject) => {
      const query = `SELECT DISTINCT name FROM ${this.tableName} WHERE payrollMonth = ?`;
      this.db.all(query, [payrollMonth], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve((rows || []).map(row => row.name));
        }
      });
    });
  }

  // Get salary summary by month
  getSalaryMonthlySummary() {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT
          payrollMonth,
          COUNT(DISTINCT name) as employeeCount,
          SUM(gross) as totalGross,
          SUM(pf) as totalPf,
          AVG(gross) as avgGross,
          AVG(pf) as avgPf,
          MIN(gross) as minGross,
          MAX(gross) as maxGross
        FROM ${this.tableName}
        GROUP BY payrollMonth
        ORDER BY payrollMonth
      `;
      this.db.all(query, [], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows || []);
        }
      });
    });
  }

  // Get salary trends for an employee
  getEmployeeSalaryTrend(employeeName) {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT
          payrollMonth,
          gross,
          pf,
          (gross + pf) as total,
          salaryMaster
        FROM ${this.tableName}
        WHERE name = ?
        ORDER BY payrollMonth
      `;
      this.db.all(query, [employeeName], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows || []);
        }
      });
    });
  }

  // Get top earners for a month
  getTopEarners(payrollMonth, limit = 10) {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT
          name,
          gross,
          pf,
          (gross + pf) as total,
          salaryMaster
        FROM ${this.tableName}
        WHERE payrollMonth = ?
        ORDER BY gross DESC
        LIMIT ?
      `;
      this.db.all(query, [payrollMonth, limit], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows || []);
        }
      });
    });
  }
}

module.exports = new SalaryModel();
