@echo off
echo ========================================
echo   Financial Report Application
echo   Stopping All Servers
echo ========================================
echo.

echo 🔍 Searching for running servers...

REM Find and kill processes on port 3000 (React frontend)
set "found3000=false"
for /f "tokens=5" %%a in ('netstat -aon ^| find ":3000" ^| find "LISTENING"') do (
    set "found3000=true"
    echo 🛑 Stopping React frontend server on port 3000 (PID: %%a)
    taskkill /f /pid %%a >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✅ Successfully stopped process %%a
    ) else (
        echo ❌ Failed to stop process %%a
    )
)

REM Find and kill processes on port 5000 (Node.js backend)
set "found5000=false"
for /f "tokens=5" %%a in ('netstat -aon ^| find ":5000" ^| find "LISTENING"') do (
    set "found5000=true"
    echo 🛑 Stopping Node.js backend server on port 5000 (PID: %%a)
    taskkill /f /pid %%a >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✅ Successfully stopped process %%a
    ) else (
        echo ❌ Failed to stop process %%a
    )
)

REM Kill any node processes that might be related to our application
echo.
echo 🔍 Checking for related Node.js processes...
for /f "tokens=2" %%a in ('tasklist /fi "imagename eq node.exe" /fo table /nh 2^>nul') do (
    REM Check if this is likely our application process
    for /f %%b in ('wmic process where "ProcessId=%%a" get CommandLine /value 2^>nul ^| find "finreport"') do (
        echo 🛑 Stopping related Node.js process (PID: %%a)
        taskkill /f /pid %%a >nul 2>&1
    )
)

REM Kill any npm processes that might be hanging
for /f "tokens=2" %%a in ('tasklist /fi "imagename eq npm.cmd" /fo table /nh 2^>nul') do (
    echo 🛑 Stopping npm process (PID: %%a)
    taskkill /f /pid %%a >nul 2>&1
)

echo.
if "%found3000%"=="false" if "%found5000%"=="false" (
    echo ℹ️  No servers were running on ports 3000 or 5000
) else (
    echo ✅ Server shutdown completed
)

echo.
echo 📊 Current port status:
netstat -an | find ":3000" | find "LISTENING" >nul 2>&1
if %errorlevel% equ 0 (
    echo ❌ Port 3000 is still in use
) else (
    echo ✅ Port 3000 is free
)

netstat -an | find ":5000" | find "LISTENING" >nul 2>&1
if %errorlevel% equ 0 (
    echo ❌ Port 5000 is still in use
) else (
    echo ✅ Port 5000 is free
)

echo.
echo ========================================
echo   Server shutdown complete
echo ========================================
pause
