console.log('Starting minimal server test...');

try {
  console.log('Loading dependencies...');
  const express = require('express');
  console.log('✅ Express loaded');
  
  const dbManager = require('../config/database');
  console.log('✅ Database manager loaded');
  
  const app = express();
  console.log('✅ Express app created');
  
  // Test database initialization
  console.log('Testing database initialization...');
  dbManager.init()
    .then(() => {
      console.log('✅ Database initialized successfully');
      
      // Test basic route
      app.get('/test', (req, res) => {
        res.json({ message: 'Server is working!' });
      });
      
      // Start server
      const PORT = 5001; // Use different port to avoid conflicts
      app.listen(PORT, () => {
        console.log(`✅ Test server running on port ${PORT}`);
        console.log('Test completed successfully!');
        process.exit(0);
      });
    })
    .catch(error => {
      console.error('❌ Database initialization failed:', error);
      process.exit(1);
    });
    
} catch (error) {
  console.error('❌ Failed to load dependencies:', error);
  process.exit(1);
}
