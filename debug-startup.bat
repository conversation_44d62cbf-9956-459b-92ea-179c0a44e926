@echo off
echo ========================================
echo   Financial Report Application
echo   Debug Startup Information
echo ========================================
echo.

echo 🔍 System Information:
echo Current Directory: %CD%
echo.

echo 🔍 Node.js Information:
node --version
if %errorlevel% neq 0 (
    echo ❌ Node.js not found
) else (
    echo ✅ Node.js is available
)
echo.

echo 🔍 NPM Information:
npm --version
if %errorlevel% neq 0 (
    echo ❌ NPM not found
) else (
    echo ✅ NPM is available
)
echo.

echo 🔍 Directory Structure:
echo Root directory contents:
dir /b
echo.

echo 🔍 Server directory check:
if exist "server" (
    echo ✅ Server directory exists
    echo Server directory contents:
    dir server /b
    echo.
    
    if exist "server\package.json" (
        echo ✅ Server package.json exists
    ) else (
        echo ❌ Server package.json missing
    )
    
    if exist "server\node_modules" (
        echo ✅ Server node_modules exists
    ) else (
        echo ❌ Server node_modules missing - run 'cd server && npm install'
    )
) else (
    echo ❌ Server directory missing
)
echo.

echo 🔍 Frontend dependencies check:
if exist "package.json" (
    echo ✅ Frontend package.json exists
) else (
    echo ❌ Frontend package.json missing
)

if exist "node_modules" (
    echo ✅ Frontend node_modules exists
) else (
    echo ❌ Frontend node_modules missing - run 'npm install'
)
echo.

echo 🔍 Database check:
if exist "server\database\finreport.db" (
    echo ✅ Database file exists
) else (
    echo ❌ Database file missing - run migration
)
echo.

echo 🔍 Port usage check:
echo Checking port 3000:
netstat -an | find ":3000" | find "LISTENING"
if %errorlevel% equ 0 (
    echo ⚠️  Port 3000 is in use
) else (
    echo ✅ Port 3000 is free
)

echo Checking port 5000:
netstat -an | find ":5000" | find "LISTENING"
if %errorlevel% equ 0 (
    echo ⚠️  Port 5000 is in use
) else (
    echo ✅ Port 5000 is free
)
echo.

echo 🔍 Package.json scripts check:
if exist "package.json" (
    echo Frontend scripts available:
    type package.json | find "scripts" -A 10
    echo.
)

if exist "server\package.json" (
    echo Backend scripts available:
    type server\package.json | find "scripts" -A 10
    echo.
)

echo ========================================
echo   Debug information complete
echo ========================================
echo.
echo 💡 Troubleshooting tips:
echo 1. If node_modules are missing, run: npm install
echo 2. If server dependencies missing, run: cd server && npm install  
echo 3. If database missing, run: cd server && npm run migrate
echo 4. If ports are in use, close other applications or restart computer
echo 5. Try start-servers-simple.bat for manual startup
echo.
pause
