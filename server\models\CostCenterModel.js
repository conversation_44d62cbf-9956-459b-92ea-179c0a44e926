const BaseModel = require('./BaseModel');

class CostCenterModel extends BaseModel {
  constructor() {
    super('cost_center');
  }

  getSearchableFields() {
    return ['name', 'costCenter', 'month'];
  }

  getDefaultOrderBy() {
    return 'createdAt DESC';
  }

  // Find by employee and month (for updates)
  findByEmployeeAndMonth(name, month) {
    return new Promise((resolve, reject) => {
      const query = `SELECT * FROM ${this.tableName} WHERE name = ? AND month = ?`;
      this.db.all(query, [name, month], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows || []);
        }
      });
    });
  }

  // Get unique values for filters
  getUniqueMonths() {
    return new Promise((resolve, reject) => {
      const query = `SELECT DISTINCT month FROM ${this.tableName} WHERE month IS NOT NULL AND month != '' ORDER BY month`;
      this.db.all(query, [], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve((rows || []).map(row => row.month));
        }
      });
    });
  }

  getUniqueEmployees() {
    return new Promise((resolve, reject) => {
      const query = `SELECT DISTINCT name FROM ${this.tableName} WHERE name IS NOT NULL AND name != '' ORDER BY name`;
      this.db.all(query, [], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve((rows || []).map(row => row.name));
        }
      });
    });
  }

  getUniqueCostCenters() {
    return new Promise((resolve, reject) => {
      const query = `SELECT DISTINCT costCenter FROM ${this.tableName} WHERE costCenter IS NOT NULL AND costCenter != '' ORDER BY costCenter`;
      this.db.all(query, [], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve((rows || []).map(row => row.costCenter));
        }
      });
    });
  }

  // Bulk upsert for Excel imports
  bulkUpsert(dataArray) {
    return new Promise((resolve, reject) => {
      if (!Array.isArray(dataArray) || dataArray.length === 0) {
        resolve({ imported: 0, updated: 0 });
        return;
      }

      this.db.serialize(() => {
        this.db.run('BEGIN TRANSACTION');

        let imported = 0;
        let updated = 0;
        let completed = 0;
        let hasError = false;

        const processItem = (item) => {
          // Check if record exists
          const selectQuery = `SELECT id FROM ${this.tableName} WHERE name = ? AND month = ?`;

          this.db.get(selectQuery, [item.name, item.month], (err, existing) => {
            if (err) {
              if (!hasError) {
                hasError = true;
                this.db.run('ROLLBACK', () => reject(err));
              }
              return;
            }

            const timestamp = new Date().toISOString();

            if (existing) {
              // Update existing record
              const updateQuery = `
                UPDATE ${this.tableName}
                SET costCenter = ?, hoursWorked = ?, updatedAt = ?, source = ?
                WHERE id = ?
              `;

              this.db.run(updateQuery, [
                item.costCenter,
                item.hoursWorked,
                timestamp,
                item.source || 'import-update',
                existing.id
              ], (updateErr) => {
                if (updateErr) {
                  if (!hasError) {
                    hasError = true;
                    this.db.run('ROLLBACK', () => reject(updateErr));
                  }
                  return;
                }

                updated++;
                completed++;

                if (completed === dataArray.length) {
                  this.db.run('COMMIT', (commitErr) => {
                    if (commitErr) {
                      reject(commitErr);
                    } else {
                      resolve({ imported, updated });
                    }
                  });
                }
              });
            } else {
              // Insert new record
              const insertQuery = `
                INSERT INTO ${this.tableName} (name, costCenter, hoursWorked, month, createdAt, updatedAt, source)
                VALUES (?, ?, ?, ?, ?, ?, ?)
              `;

              this.db.run(insertQuery, [
                item.name,
                item.costCenter,
                item.hoursWorked,
                item.month,
                timestamp,
                timestamp,
                item.source || 'import-new'
              ], (insertErr) => {
                if (insertErr) {
                  if (!hasError) {
                    hasError = true;
                    this.db.run('ROLLBACK', () => reject(insertErr));
                  }
                  return;
                }

                imported++;
                completed++;

                if (completed === dataArray.length) {
                  this.db.run('COMMIT', (commitErr) => {
                    if (commitErr) {
                      reject(commitErr);
                    } else {
                      resolve({ imported, updated });
                    }
                  });
                }
              });
            }
          });
        };

        dataArray.forEach(processItem);
      });
    });
  }

  // Get aggregated data for resource utilization
  getResourceUtilization(filters = {}) {
    return new Promise((resolve, reject) => {
      let query = `
        SELECT
          name as employee,
          month,
          costCenter,
          SUM(hoursWorked) as hours,
          (SELECT SUM(hoursWorked) FROM ${this.tableName} cc2
           WHERE cc2.name = cc1.name AND cc2.month = cc1.month) as totalHours
        FROM ${this.tableName} cc1
      `;

      const params = [];
      const conditions = [];

      // Apply filters
      if (filters.month) {
        conditions.push('month = ?');
        params.push(filters.month);
      }
      if (filters.employee) {
        conditions.push('name = ?');
        params.push(filters.employee);
      }
      if (filters.costCenter) {
        conditions.push('costCenter = ?');
        params.push(filters.costCenter);
      }

      if (conditions.length > 0) {
        query += ` WHERE ${conditions.join(' AND ')}`;
      }

      query += ` GROUP BY name, month, costCenter ORDER BY name, month, costCenter`;

      this.db.all(query, params, (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows || []);
        }
      });
    });
  }

  // Get employees for discrepancy analysis
  getEmployeesByMonth(month) {
    return new Promise((resolve, reject) => {
      const query = `SELECT DISTINCT name FROM ${this.tableName} WHERE month = ?`;
      this.db.all(query, [month], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve((rows || []).map(row => row.name));
        }
      });
    });
  }

  // Get monthly summary
  getMonthlySummary() {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT
          month,
          COUNT(DISTINCT name) as employeeCount,
          COUNT(DISTINCT costCenter) as costCenterCount,
          SUM(hoursWorked) as totalHours,
          AVG(hoursWorked) as avgHours
        FROM ${this.tableName}
        GROUP BY month
        ORDER BY month
      `;
      this.db.all(query, [], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows || []);
        }
      });
    });
  }
}

module.exports = new CostCenterModel();
