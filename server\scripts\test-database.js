const dbManager = require('../config/database');
const PaginationStateModel = require('../models/PaginationStateModel');
const path = require('path');
const fs = require('fs');

async function testDatabase() {
  console.log('🔍 Starting comprehensive database test...\n');

  try {
    // 1. Check if database file exists
    console.log('1. Checking database file existence...');
    const dbPath = process.env.DB_PATH || './database/finreport.db';
    const fullDbPath = path.resolve(dbPath);

    if (fs.existsSync(fullDbPath)) {
      const stats = fs.statSync(fullDbPath);
      console.log(`✅ Database file exists: ${fullDbPath}`);
      console.log(`   Size: ${stats.size} bytes`);
      console.log(`   Modified: ${stats.mtime}`);
    } else {
      console.log(`❌ Database file not found: ${fullDbPath}`);
      return;
    }

    // 2. Test database initialization
    console.log('\n2. Testing database initialization...');
    await dbManager.init();
    console.log('✅ Database initialized successfully');

    // 3. Test database connection
    console.log('\n3. Testing database connection...');
    const db = dbManager.getDatabase();
    if (db) {
      console.log('✅ Database connection established');
    } else {
      console.log('❌ Database connection failed');
      return;
    }

    // 4. Check database schema
    console.log('\n4. Checking database schema...');
    const tables = await new Promise((resolve, reject) => {
      db.all(`
        SELECT name FROM sqlite_master
        WHERE type='table' AND name NOT LIKE 'sqlite_%'
        ORDER BY name
      `, [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    console.log('✅ Database tables found:');
    tables.forEach(table => {
      console.log(`   - ${table.name}`);
    });

    // 5. Test pagination_state table specifically
    console.log('\n5. Testing pagination_state table...');
    const paginationTableExists = tables.some(t => t.name === 'pagination_state');

    if (paginationTableExists) {
      console.log('✅ pagination_state table exists');

      // Check table structure
      const tableInfo = await new Promise((resolve, reject) => {
        db.all(`PRAGMA table_info(pagination_state)`, [], (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        });
      });

      console.log('   Table structure:');
      tableInfo.forEach(col => {
        console.log(`     ${col.name}: ${col.type} ${col.pk ? '(PRIMARY KEY)' : ''} ${col.notnull ? '(NOT NULL)' : ''}`);
      });
    } else {
      console.log('❌ pagination_state table not found');
      return;
    }

    // 6. Test basic database operations
    console.log('\n6. Testing basic database operations...');

    // Test INSERT
    const testComponent = 'test_component';
    const testState = { currentPage: 1, itemsPerPage: 50 };

    try {
      const insertResult = await PaginationStateModel.upsert(testComponent, testState);
      console.log('✅ INSERT operation successful');
      console.log(`   Inserted: ${JSON.stringify(insertResult)}`);
    } catch (error) {
      console.log('❌ INSERT operation failed:', error.message);
    }

    // Test SELECT
    try {
      const selectResult = await PaginationStateModel.findByComponent(testComponent);
      console.log('✅ SELECT operation successful');
      console.log(`   Retrieved: ${JSON.stringify(selectResult)}`);
    } catch (error) {
      console.log('❌ SELECT operation failed:', error.message);
    }

    // 7. Test record counts
    console.log('\n7. Checking record counts...');
    for (const table of tables) {
      try {
        const count = await new Promise((resolve, reject) => {
          db.get(`SELECT COUNT(*) as count FROM ${table.name}`, [], (err, row) => {
            if (err) reject(err);
            else resolve(row.count);
          });
        });
        console.log(`   ${table.name}: ${count} records`);
      } catch (error) {
        console.log(`   ${table.name}: Error counting records - ${error.message}`);
      }
    }

    // 8. Test database permissions
    console.log('\n8. Testing database permissions...');
    try {
      // Try to create a temporary table
      await new Promise((resolve, reject) => {
        db.run(`CREATE TEMPORARY TABLE test_permissions (id INTEGER)`, [], (err) => {
          if (err) reject(err);
          else resolve();
        });
      });
      console.log('✅ Database write permissions OK');
    } catch (error) {
      console.log('❌ Database write permissions failed:', error.message);
    }

    // 9. Test SQLite3 module
    console.log('\n9. Testing SQLite3 module...');
    const sqlite3 = require('sqlite3');
    console.log(`✅ SQLite3 module loaded successfully`);
    console.log(`   Version: ${sqlite3.VERSION}`);
    console.log(`   Source ID: ${sqlite3.SOURCE_ID}`);

    console.log('\n🎉 Database test completed successfully!');

  } catch (error) {
    console.error('\n❌ Database test failed:', error);
    console.error('Stack trace:', error.stack);
  } finally {
    // Close database connection
    await dbManager.close();
    console.log('\n🔒 Database connection closed');
  }
}

// Run the test
if (require.main === module) {
  testDatabase();
}

module.exports = testDatabase;
