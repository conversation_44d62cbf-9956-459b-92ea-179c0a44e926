// src/components/CostCenter.js
import React, { useState, useEffect, useMemo, useCallback } from "react";
import {
  Container,
  Typography,
  Button,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  TableContainer,
  Paper,
  Input,
  CircularProgress,
  Box,
  Checkbox,
  Chip,
  TablePagination,
  Autocomplete,
  TextField
} from "@mui/material";
import { Link as RouterLink } from "react-router-dom";
import * as XLSX from "xlsx";
import { loadCostCenterData, saveData, STORE_NAMES, loadPaginationState, savePaginationState } from "../db";
import SearchIcon from '@mui/icons-material/Search';
import ClearIcon from '@mui/icons-material/Clear';

const CostCenter = () => {
  const [data, setData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [selectedEntries, setSelectedEntries] = useState(new Set());
  const [selectAll, setSelectAll] = useState(false);
  const [loading, setLoading] = useState(false);
  const [errorMessages, setErrorMessages] = useState([]);
  const [importSummary, setImportSummary] = useState("");
  const [selectedMonths, setSelectedMonths] = useState(["All Months"]);
  const [availableMonths, setAvailableMonths] = useState(["All Months"]);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(100);
  const [totalPages, setTotalPages] = useState(1);
  const [universalFilter, setUniversalFilter] = useState("");

  // Load data from backend API when component mounts
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setErrorMessages([]); // Clear previous errors
        console.log('Loading cost center data...');

        const fetchedData = await loadCostCenterData();
        console.log('Cost center data loaded:', fetchedData.length, 'records');

        setData(fetchedData);

        // Initialize available months from data
        const months = fetchedData.map(item => item.month);
        const unique = ["All Months", ...Array.from(new Set(months)).sort()];
        setAvailableMonths(unique);

        // Set initial filtered data
        setFilteredData(fetchedData);

        if (fetchedData.length === 0) {
          setErrorMessages(["No cost center data found. Please import data first."]);
        }
      } catch (error) {
        console.error("Error loading cost center data:", error);
        setErrorMessages([`Failed to load data: ${error.message}`]);
      } finally {
        setLoading(false);
      }
    };
    loadData();

    // Listen for database changes to reload data
    const handleDBChange = () => loadData();
    window.addEventListener("dbChange", handleDBChange);
    return () => window.removeEventListener("dbChange", handleDBChange);
  }, []);

  // Load pagination state from IndexedDB
  useEffect(() => {
    loadPaginationState("cost_center").then(state => {
      setCurrentPage(state.currentPage || 1);
      setItemsPerPage(state.itemsPerPage || 100);
    });
  }, []);

  // Save pagination state to IndexedDB
  useEffect(() => {
    savePaginationState("cost_center", {
      currentPage,
      itemsPerPage
    });
  }, [currentPage, itemsPerPage]);

  // Calculate total pages when filtered data changes
  useEffect(() => {
    const pages = Math.ceil(filteredData.length / itemsPerPage);
    setTotalPages(pages);
    // Ensure we don't go beyond the last page
    if (currentPage > pages) {
      setCurrentPage(1);
    }
  }, [filteredData, itemsPerPage, currentPage]);

  // Filter data when months or universal filter change
  useEffect(() => {
    if (data.length > 0) {
      // First filter by month
      let filtered = data;
      if (!selectedMonths.includes("All Months")) {
        filtered = data.filter(item => selectedMonths.includes(item.month));
      }

      // Then apply universal filter if it exists
      if (universalFilter.trim()) {
        const searchTerm = universalFilter.toLowerCase().trim();
        filtered = filtered.filter(item => {
          return (
            (item.name && item.name.toLowerCase().includes(searchTerm)) ||
            (item.costCenter && item.costCenter.toLowerCase().includes(searchTerm)) ||
            (item.month && item.month.toLowerCase().includes(searchTerm)) ||
            (item.hoursWorked && item.hoursWorked.toString().includes(searchTerm))
          );
        });
      }

      setFilteredData(filtered);
    } else {
      setFilteredData([]);
    }
  }, [data, selectedMonths, universalFilter]);

  // Get current page data
  const getCurrentPageData = () => {
    const start = (currentPage - 1) * itemsPerPage;
    const end = start + itemsPerPage;
    return filteredData.slice(start, end);
  };

  // Handle page change
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // Memoize the export data
  const exportDataMemo = useMemo(() => {
    if (data.length === 0) return [];

    if (selectedMonths.includes("All Months")) return data;

    return data.filter(item => selectedMonths.includes(item.month));
  }, [data, selectedMonths]);

  // Memoize the export handler
  const handleExportMemo = useCallback(() => {
    const selectedData = filteredData.filter(item => selectedEntries.has(item.id));
    if (selectedData.length === 0) {
      alert("No data available to export.");
      return;
    }

    const worksheet = XLSX.utils.json_to_sheet(selectedData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "CostCenterData");
    XLSX.writeFile(workbook, "CostCenterData.xlsx");
  }, [selectedEntries, filteredData]);



  // Handler for importing Excel file
  const handleFileUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;
    event.target.value = null; // clear the file input
    setLoading(true);
    setImportSummary("");
    setErrorMessages([]);

    const reader = new FileReader();
    reader.readAsArrayBuffer(file);
    reader.onload = async (e) => {
      try {
        const arrayBuffer = e.target.result;
        const workbook = XLSX.read(arrayBuffer, { type: "array" });
        const sheet = workbook.Sheets[workbook.SheetNames[0]];
        const parsedData = XLSX.utils.sheet_to_json(sheet, { header: 1, defval: "" });
        let errors = [];
        // let totalNonBlankRows = 0; // Removed unused variable
        let importedCount = 0;
        let updatedCount = 0;

        // Create a map of existing records for updates
        const existingRecords = new Map(data.map(item => [
          `${item.name}-${item.month}`, item
        ]));

        // Validate headers
        const expectedHeaders = ["Name", "Cost Center", "Hours Worked", "Month"];
        const headerRow = parsedData[0].map((h) => h.toString().trim());
        if (JSON.stringify(headerRow) !== JSON.stringify(expectedHeaders)) {
          errors.push("Warning: Excel file header does not match expected template. Expected headers: " +
            expectedHeaders.join(", ") + ". Found: " + headerRow.join(", "));
        }

        // Process data rows
        const newEntries = parsedData
          .slice(1)
          .map((row, index) => {
            if (row.every((cell) => cell === "")) return null;
            // totalNonBlankRows++; // Removed unused variable

            const name = row[0] ? row[0].toString().trim() : "";
            const costCenter = row[1] ? row[1].toString().trim() : "";
            const hoursWorked = row[2] ? parseFloat(row[2]) : 0;
            const month = row[3] ? row[3].toString().trim() : "";

            // Validate required fields
            if (!name || !costCenter || hoursWorked === 0 || !month) {
              errors.push(`Error in row ${index + 2}: Missing mandatory fields. Found: ${JSON.stringify(row)}`);
              return null;
            }

            // Check if record exists for update
            const recordKey = `${name}-${month}`;
            const existingRecord = existingRecords.get(recordKey);

            if (existingRecord) {
              // Update existing record
              updatedCount++;
              return {
                ...existingRecord,
                costCenter,
                hoursWorked,
                updatedAt: new Date().toISOString(),
                source: "import-update"
              };
            } else {
              // Create new record
              importedCount++;
              return {
                id: Date.now() + index,
                name,
                costCenter,
                hoursWorked,
                month,
                createdAt: new Date().toISOString(),
                source: "import-new"
              };
            }
          })
          .filter((row) => row !== null);

        // Update state with new entries
        // First remove any records that will be updated
        const updatedKeys = new Set(newEntries.map(entry => `${entry.name}-${entry.month}`));
        const filteredExisting = data.filter(item => !updatedKeys.has(`${item.name}-${item.month}`));

        const updatedData = [...filteredExisting, ...newEntries];
        setData(updatedData);

        // Auto-save the imported data to the database
        try {
          await saveData(STORE_NAMES.COST_CENTER, updatedData);

          // Generate success summary message
          const summaryMessages = [];
          if (importedCount > 0) {
            summaryMessages.push(`${importedCount} new records imported for different months`);
          }
          if (updatedCount > 0) {
            summaryMessages.push(`${updatedCount} existing records updated`);
          }
          summaryMessages.push("Data automatically saved to database.");
          if (errors.length > 0) {
            summaryMessages.push(`${errors.length} errors encountered`);
          }
          setImportSummary(summaryMessages.join(". ") + ".");

          // Set errors if any
          if (errors.length > 0) setErrorMessages(errors);
        } catch (saveError) {
          console.error("Error saving imported data:", saveError);

          // Generate error summary message
          const summaryMessages = [];
          if (importedCount > 0) {
            summaryMessages.push(`${importedCount} new records imported for different months`);
          }
          if (updatedCount > 0) {
            summaryMessages.push(`${updatedCount} existing records updated`);
          }
          summaryMessages.push("⚠️ Import successful but failed to save to database. Please click 'Save Data' manually.");
          if (errors.length > 0) {
            summaryMessages.push(`${errors.length} errors encountered`);
          }
          setImportSummary(summaryMessages.join(". ") + ".");
          setErrorMessages([...errors, `Save error: ${saveError.message}`]);
        }

      } catch (error) {
        console.error("Error processing Excel file:", error);
        setErrorMessages(["Error processing Excel file:", error.message]);
      }
      setLoading(false);
    };

    reader.onerror = () => {
      setErrorMessages(["Error reading file. Please try again.", reader.error]);
      setLoading(false);
    };
  };

  // Check if all rows are selected
  const areAllRowsSelected = () => {
    if (filteredData.length === 0) return false;
    return filteredData.every(row => selectedEntries.has(row.id));
  };

  // Check if any rows are selected
  const areSomeRowsSelected = () => {
    return selectedEntries.size > 0;
  };

  // Get row checkbox state
  const getRowCheckboxState = (id) => {
    return selectedEntries.has(id);
  };

  // Handle row selection
  const handleRowSelect = (id) => {
    setSelectedEntries(prev => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  };

  // Handle select all/deselect all
  const handleSelectAll = () => {
    const newSelectAll = !selectAll;
    setSelectAll(newSelectAll);
    if (newSelectAll) {
      const newSelected = new Set(filteredData.map(item => item.id));
      setSelectedEntries(newSelected);
    } else {
      setSelectedEntries(new Set());
    }
  };

  // Get row style based on selection
  const getRowStyle = (id) => {
    return {
      backgroundColor: getRowCheckboxState(id) ? "rgba(0, 0, 0, 0.04)" : "transparent",
    };
  };

  // Handler for selecting/deselecting a row
  const handleSelectOne = (id) => {
    handleRowSelect(id);
  };

  // Handler for deleting selected rows
  const handleDeleteSelected = () => {
    const newEntries = data.filter((row) => !selectedEntries.has(row.id));
    setData(newEntries);
    setSelectedEntries(new Set());
    setSelectAll(false);
  };

  // Function to save data to IndexedDB
  const handleSaveData = async () => {
    try {
      setLoading(true);
      await saveData(STORE_NAMES.COST_CENTER, data);
      setErrorMessages(["Data saved successfully!"]);
    } catch (error) {
      console.error("Error saving cost center data:", error);
      setErrorMessages([error.message]);
    } finally {
      setLoading(false);
    }
  };

  // Enhanced month selection dropdown
  const MonthDropdown = () => (
    <Autocomplete
      multiple
      value={selectedMonths}
      onChange={(_, newValue) => {
        setSelectedMonths(newValue);
      }}
      options={availableMonths}
      getOptionLabel={(option) => option}
      renderInput={(params) => (
        <TextField
          {...params}
          label="Select Months"
          placeholder="Select months..."
          fullWidth
          InputProps={{
            ...params.InputProps,
            startAdornment: (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                {params.InputProps.startAdornment}
                <Button
                  size="small"
                  onClick={() => {
                    if (selectedMonths.length === availableMonths.length) {
                      setSelectedMonths([]);
                    } else {
                      setSelectedMonths(availableMonths);
                    }
                  }}
                  sx={{
                    color: selectedMonths.length === availableMonths.length
                      ? '#1976d2'
                      : '#1976d2',
                  }}
                >
                  {selectedMonths.length === availableMonths.length
                    ? 'Deselect All'
                    : 'Select All'}
                </Button>
              </Box>
            )
          }}
        />
      )}
      renderOption={(props, option, { selected }) => (
        <li {...props}>
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              mr: 1,
            }}
          >
            <Checkbox
              checked={selected}
              sx={{
                color: '#1976d2',
                '&.Mui-checked': {
                  color: '#1976d2',
                },
              }}
            />
          </Box>
          {option}
        </li>
      )}
      renderTags={(value, getTagProps) =>
        value.map((option, index) => (
          <Chip
            variant="outlined"
            label={option}
            {...getTagProps({ index })}
            size="small"
            sx={{
              color: '#1976d2',
              borderColor: '#1976d2',
              '& .MuiChip-deleteIcon': {
                color: '#1976d2',
              }
            }}
          />
        ))
      }
      PopperProps={{
        sx: {
          maxHeight: 400,
          overflow: 'auto',
          '& .MuiAutocomplete-popper': {
            width: '100%'
          }
        }
      }}
      filterOptions={(options, params) => {
        const filtered = options.filter((option) =>
          option.toLowerCase().includes(params.inputValue.toLowerCase())
        );
        return filtered;
      }}
      disableCloseOnSelect
    />
  );

  return (
    <Container sx={{ padding: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h5" component="h2">
          Cost Center Data
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <MonthDropdown />
        </Box>
      </Box>

      {/* Universal Filter */}
      <Box sx={{ mb: 3 }}>
        <TextField
          fullWidth
          label="Universal Filter"
          placeholder="Search across all fields..."
          value={universalFilter}
          onChange={(e) => setUniversalFilter(e.target.value)}
          variant="outlined"
          InputProps={{
            startAdornment: (
              <Box sx={{ mr: 1, color: 'action.active' }}>
                <SearchIcon />
              </Box>
            ),
            endAdornment: universalFilter ? (
              <Box sx={{ ml: 1, cursor: 'pointer' }} onClick={() => setUniversalFilter('')}>
                <ClearIcon />
              </Box>
            ) : null
          }}
        />
        {universalFilter && (
          <Typography variant="caption" sx={{ mt: 1, display: 'block', color: 'text.secondary' }}>
            Found {filteredData.length} records matching "{universalFilter}"
          </Typography>
        )}
      </Box>
      <Box sx={{ mb: 2 }}>
        <Button variant="outlined" component={RouterLink} to="/">
          Back to Dashboard
        </Button>
      </Box>
      <Button variant="contained" component="label" sx={{ mb: 2, mr: 2 }}>
        Import Excel
        <Input type="file" accept=".xlsx, .xls" onChange={handleFileUpload} sx={{ display: "none" }} />
      </Button>
      <Button variant="contained" onClick={handleExportMemo} sx={{ mb: 2, mr: 2 }}>
        Export Excel
      </Button>
      <Button variant="contained" color="error" onClick={handleDeleteSelected} sx={{ mb: 2 }}>
        Delete Selected
      </Button>
      <Button
        variant="contained"
        color="primary"
        onClick={handleSaveData}
        disabled={loading}
        startIcon={loading ? <CircularProgress size={20} /> : null}
        sx={{ mb: 2 }}
      >
        Save Data
      </Button>
      {loading && (
        <Box display="flex" alignItems="center" mt={2}>
          <CircularProgress />
          <Typography variant="body1" sx={{ ml: 2 }}>
            Loading...
          </Typography>
        </Box>
      )}
      {importSummary && (
        <Box sx={{ mt: 2, p: 2, backgroundColor: "#fff3cd" }}>
          <Typography variant="h6">Import Summary</Typography>
          <Typography>{importSummary}</Typography>
          {errorMessages.map((err, idx) => (
            <Typography key={idx} variant="body2">
              {err}
            </Typography>
          ))}
        </Box>
      )}
      <TableContainer component={Paper} sx={{ mt: 2 }}>
        <Table>
          <TableHead sx={{ backgroundColor: "#1976d2" }}>
            <TableRow>
              <TableCell padding="checkbox" sx={{ width: 48 }}>
                <Checkbox
                  checked={areAllRowsSelected()}
                  indeterminate={areSomeRowsSelected() && !areAllRowsSelected()}
                  onChange={handleSelectAll}
                  sx={{
                    color: "#1976d2",
                    '&.Mui-checked': {
                      color: "#1976d2",
                    },
                    '&.MuiCheckbox-indeterminate': {
                      color: "#1976d2",
                    }
                  }}
                />
              </TableCell>
              <TableCell sx={{ color: "black", fontWeight: "bold" }}>Name</TableCell>
              <TableCell sx={{ color: "black", fontWeight: "bold" }}>Cost Center</TableCell>
              <TableCell sx={{ color: "black", fontWeight: "bold" }}>Hours Worked</TableCell>
              <TableCell sx={{ color: "black", fontWeight: "bold" }}>Month</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {getCurrentPageData().length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} align="center">
                  No data available for the selected month. Import an Excel file to add data.
                </TableCell>
              </TableRow>
            ) : (
              getCurrentPageData().map((row) => (
                <TableRow key={row.id} sx={getRowStyle(row.id)}>
                  <TableCell padding="checkbox" sx={{ width: 48 }}>
                    <Checkbox
                      checked={getRowCheckboxState(row.id)}
                      onChange={() => handleSelectOne(row.id)}
                      sx={{
                        color: "#1976d2",
                        '&.Mui-checked': {
                          color: "#1976d2",
                        },
                        '&.MuiCheckbox-indeterminate': {
                          color: "#1976d2",
                        }
                      }}
                    />
                  </TableCell>
                  <TableCell>{row.name}</TableCell>
                  <TableCell>{row.costCenter}</TableCell>
                  <TableCell>{row.hoursWorked}</TableCell>
                  <TableCell>{row.month}</TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>
      {totalPages > 1 && (
        <TablePagination
          component="div"
          count={filteredData.length}
          rowsPerPage={itemsPerPage}
          page={currentPage - 1}
          onPageChange={(_, newPage) => handlePageChange(newPage + 1)}
          rowsPerPageOptions={[100]}
          labelRowsPerPage="Entries per page:"
        />
      )}
    </Container>
  );
};

export default CostCenter;