import React from 'react';
import {
  Box,
  Skeleton,
  // Paper, // Removed unused import
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Card,
  CardContent,
  useTheme,
  useMediaQuery
} from '@mui/material';

export default function TableSkeleton({
  rows = 5,
  columns = 5,
  showTableHeader = true,
  headerHeight = 56,
  rowHeight = 53,
  showToolbar = true,
  showPagination = true,
  animation = 'pulse',
  dense = false,
}) {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // Adjust for mobile
  const actualColumns = isMobile ? Math.min(3, columns) : columns;

  return (
    <Card sx={{ width: '100%', overflow: 'hidden' }}>
      {showToolbar && (
        <CardContent sx={{ p: 2, pb: '16px !important' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Skeleton variant="text" width={200} height={32} animation={animation} />
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Skeleton variant="rounded" width={120} height={40} animation={animation} />
              <Skeleton variant="circular" width={40} height={40} animation={animation} />
            </Box>
          </Box>
        </CardContent>
      )}

      <TableContainer>
        <Table size={dense ? 'small' : 'medium'}>
          {showTableHeader && (
            <TableHead>
              <TableRow>
                {Array(actualColumns).fill(0).map((_, index) => (
                  <TableCell key={index}>
                    <Skeleton
                      variant="text"
                      height={headerHeight * 0.6}
                      animation={animation}
                      sx={{ borderRadius: 1 }}
                    />
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
          )}
          <TableBody>
            {Array(rows).fill(0).map((_, rowIndex) => (
              <TableRow key={rowIndex}>
                {Array(actualColumns).fill(0).map((_, colIndex) => (
                  <TableCell key={colIndex}>
                    <Skeleton
                      variant="text"
                      height={rowHeight * 0.6}
                      animation={animation}
                      width={colIndex === 0 ? '60%' : '80%'} // First column usually shorter
                      sx={{ borderRadius: 1 }}
                    />
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {showPagination && (
        <Box sx={{ p: 2, display: 'flex', justifyContent: 'flex-end' }}>
          <Skeleton variant="rounded" width={isMobile ? 200 : 300} height={40} animation={animation} />
        </Box>
      )}
    </Card>
  );
}
