const Joi = require('joi');

// Validation schemas
const schemas = {
  costCenter: Joi.object({
    name: Joi.string().trim().min(1).max(255).required(),
    costCenter: Joi.string().trim().min(1).max(255).required(),
    hoursWorked: Joi.number().positive().required(),
    month: Joi.string().trim().min(1).max(50).required(),
    source: Joi.string().valid('manual', 'import-new', 'import-update').default('manual')
  }),

  salary: Joi.object({
    payrollMonth: Joi.string().trim().min(1).max(50).required(),
    name: Joi.string().trim().min(1).max(255).required(),
    gross: Joi.number().positive().required(),
    pf: Joi.number().min(0).default(0),
    salaryMaster: Joi.string().trim().max(255).allow('', null),
    source: Joi.string().valid('manual', 'import-new', 'import-update').default('manual'),
    // Allow additional fields that will be stripped
    id: Joi.any().optional(),
    slNo: Joi.any().optional(),
    createdAt: Joi.any().optional(),
    updatedAt: Joi.any().optional()
  }),

  salaryMaster: Joi.object({
    name: Joi.string().trim().min(1).max(255).required(),
    designation: Joi.string().trim().max(255).allow('', null),
    department: Joi.string().trim().max(255).allow('', null),
    baseSalary: Joi.number().positive().allow(null)
  }),

  paginationState: Joi.object({
    currentPage: Joi.number().integer().min(1).default(1),
    itemsPerPage: Joi.number().integer().min(1).max(1000).default(100)
  }),

  costCenterMapping: Joi.object({
    originalName: Joi.string().trim().min(1).max(255).required(),
    alternateName: Joi.string().trim().min(1).max(255).required()
  }),

  // Query parameter validation
  queryParams: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(1000).default(100),
    month: Joi.string().trim().allow(''),
    employee: Joi.string().trim().allow(''),
    costCenter: Joi.string().trim().allow(''),
    search: Joi.string().trim().allow('')
  })
};

// Validation middleware factory
const validate = (schema, property = 'body') => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req[property], {
      abortEarly: false,
      stripUnknown: true,
      convert: true
    });

    if (error) {
      const errors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }));

      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors
      });
    }

    req[property] = value;
    next();
  };
};

// Bulk validation for array data
const validateBulk = (schema) => {
  return (req, res, next) => {
    if (!Array.isArray(req.body)) {
      return res.status(400).json({
        success: false,
        message: 'Request body must be an array'
      });
    }

    const errors = [];
    const validatedData = [];

    req.body.forEach((item, index) => {
      const { error, value } = schema.validate(item, {
        abortEarly: false,
        stripUnknown: true,
        convert: true
      });

      if (error) {
        errors.push({
          index,
          errors: error.details.map(detail => ({
            field: detail.path.join('.'),
            message: detail.message
          }))
        });
      } else {
        validatedData.push(value);
      }
    });

    if (errors.length > 0) {
      return res.status(400).json({
        success: false,
        message: `Bulk validation failed: ${errors.length} record(s) have validation errors`,
        errors,
        details: `Please check the following records: ${errors.map(e => `Row ${e.index + 1}`).join(', ')}`
      });
    }

    req.body = validatedData;
    next();
  };
};

module.exports = {
  schemas,
  validate,
  validateBulk
};
