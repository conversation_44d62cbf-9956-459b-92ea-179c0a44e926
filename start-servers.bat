@echo off
echo ========================================
echo   Financial Report Application
echo   Starting Backend and Frontend Servers
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Check if npm is available
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: npm is not available
    echo Please ensure Node.js is properly installed
    pause
    exit /b 1
)

echo ✅ Node.js and npm are available
echo.

REM Check if server dependencies are installed
if not exist "server\node_modules" (
    echo 📦 Installing backend dependencies...
    cd server
    npm install
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install backend dependencies
        cd ..
        pause
        exit /b 1
    )
    cd ..
    echo ✅ Backend dependencies installed
    echo.
)

REM Check if frontend dependencies are installed
if not exist "node_modules" (
    echo 📦 Installing frontend dependencies...
    npm install
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install frontend dependencies
        pause
        exit /b 1
    )
    echo ✅ Frontend dependencies installed
    echo.
) else (
    echo ✅ Frontend dependencies already installed
)

REM Check if concurrently is available
npm list concurrently >nul 2>&1
if %errorlevel% neq 0 (
    echo 📦 Installing concurrently for development...
    npm install concurrently --save-dev
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install concurrently
        pause
        exit /b 1
    )
    echo ✅ Concurrently installed
    echo.
)

REM Check if database is initialized
if not exist "server\database\finreport.db" (
    echo 🗄️  Initializing database...
    cd server
    npm run migrate
    if %errorlevel% neq 0 (
        echo ERROR: Database migration failed
        cd ..
        pause
        exit /b 1
    )
    cd ..
    echo ✅ Database initialized
    echo.
)

REM Kill any existing processes on ports 3000 and 5000
echo 🔄 Checking for existing processes...
for /f "tokens=5" %%a in ('netstat -aon ^| find ":3000" ^| find "LISTENING"') do (
    echo Stopping process on port 3000 (PID: %%a)
    taskkill /f /pid %%a >nul 2>&1
)

for /f "tokens=5" %%a in ('netstat -aon ^| find ":5000" ^| find "LISTENING"') do (
    echo Stopping process on port 5000 (PID: %%a)
    taskkill /f /pid %%a >nul 2>&1
)

echo.
echo 🚀 Starting servers...
echo.
echo 📍 Backend Server: http://localhost:5000
echo 📍 Frontend Server: http://localhost:3000
echo.
echo Press Ctrl+C to stop both servers
echo ========================================
echo.

REM Start both servers using npm run start:dev (concurrently)
echo 🚀 Starting both servers with concurrently...
npm run start:dev

REM If concurrently fails, try manual startup
if %errorlevel% neq 0 (
    echo.
    echo ⚠️  Concurrently failed, trying manual startup...
    echo.
    echo 🔧 Starting backend server...
    start "Backend Server" cmd /k "cd server && npm start"

    echo ⏳ Waiting 5 seconds for backend to start...
    timeout /t 5 /nobreak >nul

    echo 🔧 Starting frontend server...
    start "Frontend Server" cmd /k "npm start"

    echo.
    echo ✅ Servers started in separate windows
    echo 📍 Backend: http://localhost:5000
    echo 📍 Frontend: http://localhost:3000
    echo.
    echo Close the server windows to stop the servers
)

echo.
echo ========================================
echo   Server startup completed
echo ========================================
pause
