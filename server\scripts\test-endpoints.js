const axios = require('axios');

const API_BASE_URL = 'http://localhost:5000/api';

async function testEndpoints() {
  console.log('🔍 Testing API endpoints...\n');

  try {
    // Test database status
    console.log('1. Testing database status...');
    const dbStatus = await axios.get(`${API_BASE_URL}/database/status`);
    console.log('✅ Database status:', dbStatus.data.success);
    console.log('   Tables:', dbStatus.data.data?.tables?.length || 0);

    // Test cost center endpoints
    console.log('\n2. Testing cost center endpoints...');
    
    // Test paginated endpoint
    const costCenterPaginated = await axios.get(`${API_BASE_URL}/cost-center?page=1&limit=10`);
    console.log('✅ Cost center (paginated):', costCenterPaginated.data.success);
    console.log('   Records:', costCenterPaginated.data.data?.length || 0);
    console.log('   Total:', costCenterPaginated.data.pagination?.total || 0);

    // Test all endpoint
    const costCenterAll = await axios.get(`${API_BASE_URL}/cost-center/all`);
    console.log('✅ Cost center (all):', costCenterAll.data.success);
    console.log('   Records:', costCenterAll.data.data?.length || 0);
    console.log('   Total:', costCenterAll.data.total || 0);

    // Test salary endpoints
    console.log('\n3. Testing salary endpoints...');
    
    // Test paginated endpoint
    const salaryPaginated = await axios.get(`${API_BASE_URL}/salary?page=1&limit=10`);
    console.log('✅ Salary (paginated):', salaryPaginated.data.success);
    console.log('   Records:', salaryPaginated.data.data?.length || 0);
    console.log('   Total:', salaryPaginated.data.pagination?.total || 0);

    // Test all endpoint
    const salaryAll = await axios.get(`${API_BASE_URL}/salary/all`);
    console.log('✅ Salary (all):', salaryAll.data.success);
    console.log('   Records:', salaryAll.data.data?.length || 0);
    console.log('   Total:', salaryAll.data.total || 0);

    // Test pagination state endpoints
    console.log('\n4. Testing pagination state endpoints...');
    
    // Test save pagination state
    const saveResult = await axios.post(`${API_BASE_URL}/pagination-state/test_component`, {
      currentPage: 2,
      itemsPerPage: 50
    });
    console.log('✅ Save pagination state:', saveResult.data.success);

    // Test load pagination state
    const loadResult = await axios.get(`${API_BASE_URL}/pagination-state/test_component`);
    console.log('✅ Load pagination state:', loadResult.data.success);
    console.log('   State:', loadResult.data.data);

    console.log('\n🎉 All endpoint tests completed successfully!');

  } catch (error) {
    console.error('\n❌ Endpoint test failed:', error.message);
    if (error.response) {
      console.error('   Status:', error.response.status);
      console.error('   Data:', error.response.data);
    }
  }
}

// Run the test
if (require.main === module) {
  testEndpoints();
}

module.exports = testEndpoints;
