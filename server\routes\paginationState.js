const express = require('express');
const router = express.Router();
const PaginationStateModel = require('../models/PaginationStateModel');
const { validate, schemas } = require('../middleware/validation');
const { asyncHandler } = require('../middleware/errorHandler');

// GET /api/pagination-state/:component - Get pagination state for component
router.get('/:component', asyncHandler(async (req, res) => {
  const { component } = req.params;
  const state = await PaginationStateModel.findByComponent(component);

  res.json({
    success: true,
    data: state
  });
}));

// POST /api/pagination-state/:component - Save pagination state for component
router.post('/:component', validate(schemas.paginationState), asyncHandler(async (req, res) => {
  const { component } = req.params;
  console.log('Pagination state request:', { component, body: req.body });
  const state = await PaginationStateModel.upsert(component, req.body);

  res.json({
    success: true,
    data: state,
    message: 'Pagination state saved successfully'
  });
}));

// GET /api/pagination-state - Get all pagination states
router.get('/', asyncHandler(async (req, res) => {
  const states = await PaginationStateModel.getAllStates();

  res.json({
    success: true,
    data: states
  });
}));

// DELETE /api/pagination-state/:component - Delete pagination state for component
router.delete('/:component', asyncHandler(async (req, res) => {
  const { component } = req.params;
  const result = await PaginationStateModel.deleteByComponent(component);

  res.json({
    success: true,
    data: result,
    message: 'Pagination state deleted successfully'
  });
}));

// POST /api/pagination-state/reset - Reset all pagination states
router.post('/reset', asyncHandler(async (req, res) => {
  const result = await PaginationStateModel.resetAll();

  res.json({
    success: true,
    data: result,
    message: 'All pagination states reset successfully'
  });
}));

module.exports = router;
