// Database migration script
require('dotenv').config();
const dbManager = require('../config/database');

async function migrate() {
  try {
    console.log('🚀 Starting database migration...');

    // Initialize database (creates tables if they don't exist)
    await dbManager.init();

    console.log('✅ Database migration completed successfully!');
    console.log('📊 Tables created:');
    console.log('   - cost_center');
    console.log('   - salary');
    console.log('   - salary_master');
    console.log('   - resource_utilization');
    console.log('   - resource_cost');
    console.log('   - employee_discrepancy');
    console.log('   - pagination_state');

    console.log('\n🔗 Database location:', process.env.DB_PATH || './database/finreport.db');
    console.log('🌐 Server ready to start on port:', process.env.PORT || 5000);

    process.exit(0);
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

migrate();
