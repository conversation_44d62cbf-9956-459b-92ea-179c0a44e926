// Utility functions for cost center mappings

/**
 * Apply cost center mappings to a cost center name (case-insensitive matching)
 * @param {string} originalName - The original cost center name
 * @param {Object} mappings - Object with originalName as key and alternateName as value
 * @returns {string} - The alternate name if mapping exists, otherwise the original name
 */
export const applyCostCenterMapping = (originalName, mappings = {}) => {
  if (!originalName || typeof originalName !== 'string') {
    return originalName || '';
  }

  const trimmedName = originalName.trim();

  // First try exact match (for backward compatibility)
  if (mappings[trimmedName]) {
    return mappings[trimmedName];
  }

  // Then try case-insensitive match
  const lowerTrimmed = trimmedName.toLowerCase();
  for (const [key, value] of Object.entries(mappings)) {
    if (key.toLowerCase() === lowerTrimmed) {
      return value;
    }
  }

  // Return original if no mapping found
  return trimmedName;
};

/**
 * Apply cost center mappings to an array of data objects
 * @param {Array} data - Array of objects containing cost center data
 * @param {Object} mappings - Object with originalName as key and alternateName as value
 * @param {string} costCenterField - The field name that contains the cost center (default: 'costCenter')
 * @returns {Array} - Array with mapped cost center names
 */
export const applyCostCenterMappingsToData = (data, mappings = {}, costCenterField = 'costCenter') => {
  if (!Array.isArray(data)) {
    return data;
  }

  return data.map(item => ({
    ...item,
    [costCenterField]: applyCostCenterMapping(item[costCenterField], mappings),
    // Keep original for reference if needed
    [`original${costCenterField.charAt(0).toUpperCase() + costCenterField.slice(1)}`]: item[costCenterField],
  }));
};

/**
 * Get unique cost centers from data array (case-insensitive)
 * @param {Array} data - Array of objects containing cost center data
 * @param {string} costCenterField - The field name that contains the cost center (default: 'costCenter')
 * @returns {Array} - Array of unique cost center names with preserved casing
 */
export const getUniqueCostCenters = (data, costCenterField = 'costCenter') => {
  if (!Array.isArray(data)) {
    console.warn('getUniqueCostCenters: data is not an array', data);
    return [];
  }

  // console.log(`getUniqueCostCenters: Processing ${data.length} records`);

  // Map to store case-insensitive keys with their preferred casing and frequency
  const costCenterMap = new Map();

  data.forEach((item, index) => {
    const costCenter = item[costCenterField];

    if (!costCenter || typeof costCenter !== 'string') {
      return; // Skip invalid entries
    }

    const trimmed = costCenter.trim();
    if (trimmed === '') {
      return; // Skip empty entries
    }

    const lowerKey = trimmed.toLowerCase();

    if (costCenterMap.has(lowerKey)) {
      // Increment frequency for existing entry
      const existing = costCenterMap.get(lowerKey);
      existing.frequency++;

      // Keep the most common casing, or first occurrence if tied
      if (existing.frequency === 1) {
        existing.preferredCasing = trimmed;
      }
    } else {
      // Add new entry
      costCenterMap.set(lowerKey, {
        preferredCasing: trimmed,
        frequency: 1,
        firstSeen: index
      });
    }
  });

  // Extract unique cost centers with preferred casing
  const uniqueCostCenters = Array.from(costCenterMap.values())
    .map(entry => entry.preferredCasing)
    .sort((a, b) => a.localeCompare(b, undefined, { sensitivity: 'base' }));

  console.log(`getUniqueCostCenters: Found ${uniqueCostCenters.length} unique cost centers`);

  return uniqueCostCenters;
};

/**
 * Create a display name for cost center that shows both original and alternate if different
 * @param {string} originalName - The original cost center name
 * @param {Object} mappings - Object with originalName as key and alternateName as value
 * @param {boolean} showBoth - Whether to show both names when they differ (default: false)
 * @returns {string} - The display name
 */
export const getCostCenterDisplayName = (originalName, mappings = {}, showBoth = false) => {
  if (!originalName || typeof originalName !== 'string') {
    return originalName || '';
  }

  const trimmedName = originalName.trim();
  const alternateName = mappings[trimmedName];

  if (!alternateName || alternateName === trimmedName) {
    return trimmedName;
  }

  if (showBoth) {
    return `${alternateName} (${trimmedName})`;
  }

  return alternateName;
};

/**
 * Validate cost center mappings
 * @param {Array} mappings - Array of mapping objects with originalName and alternateName
 * @returns {Object} - Validation result with isValid boolean and errors array
 */
export const validateCostCenterMappings = (mappings) => {
  const errors = [];
  const originalNames = new Set();
  const alternateNames = new Set();

  if (!Array.isArray(mappings)) {
    return { isValid: false, errors: ['Mappings must be an array'] };
  }

  mappings.forEach((mapping, index) => {
    const { originalName, alternateName } = mapping;

    // Check required fields
    if (!originalName || typeof originalName !== 'string' || originalName.trim() === '') {
      errors.push(`Mapping ${index + 1}: Original name is required`);
    }

    if (!alternateName || typeof alternateName !== 'string' || alternateName.trim() === '') {
      errors.push(`Mapping ${index + 1}: Alternate name is required`);
    }

    if (originalName && alternateName) {
      const trimmedOriginal = originalName.trim();
      const trimmedAlternate = alternateName.trim();

      // Check for duplicate original names
      if (originalNames.has(trimmedOriginal)) {
        errors.push(`Mapping ${index + 1}: Duplicate original name '${trimmedOriginal}'`);
      } else {
        originalNames.add(trimmedOriginal);
      }

      // Check for duplicate alternate names
      if (alternateNames.has(trimmedAlternate)) {
        errors.push(`Mapping ${index + 1}: Duplicate alternate name '${trimmedAlternate}'`);
      } else {
        alternateNames.add(trimmedAlternate);
      }

      // Check if original and alternate are the same
      if (trimmedOriginal === trimmedAlternate) {
        errors.push(`Mapping ${index + 1}: Original and alternate names cannot be the same`);
      }
    }
  });

  return {
    isValid: errors.length === 0,
    errors
  };
};
