// Test script to verify salary bulk endpoint fix
const axios = require('axios');

const API_BASE_URL = 'http://localhost:5000/api';

// Test data that should pass validation
const testData = [
  {
    payrollMonth: "2024-01",
    name: "<PERSON>",
    gross: 50000,
    pf: 5000,
    salaryMaster: "Engineering",
    source: "import-new"
  },
  {
    payrollMonth: "2024-01",
    name: "<PERSON>",
    gross: 60000,
    pf: 6000,
    salaryMaster: "Marketing",
    source: "import-new"
  }
];

async function testSalaryBulkEndpoint() {
  try {
    console.log('Testing salary bulk endpoint...');
    console.log('Test data:', JSON.stringify(testData, null, 2));
    
    const response = await axios.post(`${API_BASE_URL}/salary/bulk`, testData, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });
    
    console.log('✅ Success!');
    console.log('Response:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.log('❌ Error occurred:');
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Response data:', JSON.stringify(error.response.data, null, 2));
    } else if (error.request) {
      console.log('No response received:', error.message);
    } else {
      console.log('Error:', error.message);
    }
  }
}

// Test with invalid data to check validation
const invalidTestData = [
  {
    payrollMonth: "", // Invalid: empty
    name: "Test User",
    gross: -1000, // Invalid: negative
    pf: "invalid", // Invalid: not a number
    salaryMaster: "Test",
    source: "import-new"
  }
];

async function testValidationErrors() {
  try {
    console.log('\nTesting validation errors...');
    console.log('Invalid test data:', JSON.stringify(invalidTestData, null, 2));
    
    const response = await axios.post(`${API_BASE_URL}/salary/bulk`, invalidTestData, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });
    
    console.log('⚠️ Unexpected success - validation should have failed');
    console.log('Response:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.log('✅ Validation correctly failed!');
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Response data:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

async function runTests() {
  console.log('Starting salary bulk endpoint tests...\n');
  
  // Wait a moment for server to be ready
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  await testSalaryBulkEndpoint();
  await testValidationErrors();
  
  console.log('\nTests completed!');
}

runTests().catch(console.error);
