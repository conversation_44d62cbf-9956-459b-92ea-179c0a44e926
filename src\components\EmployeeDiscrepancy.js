// src/components/EmployeeDiscrepancy.js
import React, { useState, useEffect, useCallback } from "react";
import {
  Container,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Button,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  TableContainer,
  Paper,
  CircularProgress,
} from "@mui/material";
import { Link } from "react-router-dom";
import * as XLSX from "xlsx";
import { loadCostCenterData, loadSalaryData } from "../db";

const normalizeName = (name) => (name ? name.trim().toLowerCase() : "");
const getSalaryMonth = (record) => {
  if (record.payrollMonth && typeof record.payrollMonth === "string") return record.payrollMonth.trim();
  if (record["Payroll Month"] && typeof record["Payroll Month"] === "string") return record["Payroll Month"].trim();
  return "";
};

const EmployeeDiscrepancy = () => {
  const [selectedMonth, setSelectedMonth] = useState("");
  const [uniqueMonths, setUniqueMonths] = useState([]);
  const [missingInSalary, setMissingInSalary] = useState([]);
  const [missingInCost, setMissingInCost] = useState([]);
  const [loading, setLoading] = useState(false);

  const fetchUniqueMonths = useCallback(async () => {
    const ccData = await loadCostCenterData();
    const months = ccData
      .filter((record) => record.month && record.month.trim() !== "")
      .map((record) => record.month.trim());
    const unique = [...new Set(months)];
    setUniqueMonths(unique);
    if (!selectedMonth && unique.length > 0) setSelectedMonth(unique[0]);
    else if (selectedMonth && !unique.includes(selectedMonth)) setSelectedMonth("");
  }, [selectedMonth]);

  // Refresh unique months on mount and when DB changes.
  useEffect(() => {
    fetchUniqueMonths();
    const handleDBChange = () => {
      fetchUniqueMonths();
    };
    window.addEventListener("dbChange", handleDBChange);
    return () => window.removeEventListener("dbChange", handleDBChange);
  }, [fetchUniqueMonths]);

  const computeDiscrepancies = useCallback(async () => {
    if (!selectedMonth) return;
    setLoading(true);
    try {
      const ccData = await loadCostCenterData();
      const salaryData = await loadSalaryData();

      const filteredCC = ccData.filter(
        (r) => r.month && r.month.trim() === selectedMonth && r.name && r.name.trim() !== ""
      );
      const filteredSalary = salaryData.filter(
        (r) => getSalaryMonth(r) === selectedMonth && r.name && r.name.trim() !== ""
      );

      const ccNames = new Set(filteredCC.map((r) => normalizeName(r.name)));
      const salaryNames = new Set(filteredSalary.map((r) => normalizeName(r.name)));

      const missingSalary = [...ccNames].filter((name) => !salaryNames.has(name));
      const missingCost = [...salaryNames].filter((name) => !ccNames.has(name));

      setMissingInSalary(missingSalary);
      setMissingInCost(missingCost);
    } catch (error) {
      console.error("Error computing discrepancies:", error);
    }
    setLoading(false);
  }, [selectedMonth]);

  useEffect(() => {
    // Compute discrepancies when selected month changes
    computeDiscrepancies();
  }, [selectedMonth, computeDiscrepancies]);

  const handleExport = () => {
    const wb = XLSX.utils.book_new();
    const ws1Data = [
      ["Employees in Cost Center but not in Salary"],
      ...missingInSalary.map((name) => [name]),
    ];
    const ws2Data = [
      ["Employees in Salary but not in Cost Center"],
      ...missingInCost.map((name) => [name]),
    ];
    const ws1 = XLSX.utils.aoa_to_sheet(ws1Data);
    const ws2 = XLSX.utils.aoa_to_sheet(ws2Data);
    XLSX.utils.book_append_sheet(wb, ws1, "CostCenterMissingSalary");
    XLSX.utils.book_append_sheet(wb, ws2, "SalaryMissingCostCenter");
    XLSX.writeFile(wb, `EmployeeDiscrepancies_${selectedMonth}.xlsx`);
  };

  return (
    <Container sx={{ padding: 4 }}>
      <Box sx={{ mb: 2 }}>
        <Button variant="outlined" component={Link} to="/">
          Back to Dashboard
        </Button>
      </Box>
      <Typography variant="h4" gutterBottom>
        Employee Discrepancies for {selectedMonth}
      </Typography>
      <FormControl sx={{ minWidth: 200, marginBottom: 2 }}>
        <InputLabel id="ed-month-label">Select Month</InputLabel>
        <Select
          labelId="ed-month-label"
          value={selectedMonth}
          label="Select Month"
          onChange={(e) => setSelectedMonth(e.target.value)}
        >
          {uniqueMonths.map((m, idx) => (
            <MenuItem key={idx} value={m}>
              {m}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
      <Box sx={{ mb: 2 }}>
        <Button variant="contained" onClick={computeDiscrepancies}>
          Compute Discrepancies
        </Button>
      </Box>
      {loading && (
        <Box display="flex" alignItems="center" mt={2}>
          <CircularProgress />
          <Typography variant="body1" sx={{ ml: 2 }}>
            Computing...
          </Typography>
        </Box>
      )}
      <Box sx={{ mt: 2 }}>
        <Typography variant="h5" gutterBottom>
          Employees in Cost Center but not in Salary
        </Typography>
        {missingInSalary.length > 0 ? (
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>#</TableCell>
                  <TableCell>Employee</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {missingInSalary.map((name, idx) => (
                  <TableRow key={idx}>
                    <TableCell>{idx + 1}</TableCell>
                    <TableCell>{name}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        ) : (
          <Typography>No discrepancies found in this category.</Typography>
        )}
      </Box>
      <Box sx={{ mt: 4 }}>
        <Typography variant="h5" gutterBottom>
          Employees in Salary but not in Cost Center
        </Typography>
        {missingInCost.length > 0 ? (
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>#</TableCell>
                  <TableCell>Employee</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {missingInCost.map((name, idx) => (
                  <TableRow key={idx}>
                    <TableCell>{idx + 1}</TableCell>
                    <TableCell>{name}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        ) : (
          <Typography>No discrepancies found in this category.</Typography>
        )}
      </Box>
      <Box sx={{ mt: 2 }}>
        <Button variant="contained" onClick={handleExport}>
          Export as Excel
        </Button>
      </Box>
    </Container>
  );
};

export default EmployeeDiscrepancy;
