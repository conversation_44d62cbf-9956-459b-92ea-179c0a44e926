const express = require('express');
const router = express.Router();
const SalaryModel = require('../models/SalaryModel');
const { validate, validateBulk, schemas } = require('../middleware/validation');
const { asyncHandler } = require('../middleware/errorHandler');

// GET /api/salary/all - Get all salary records without pagination
router.get('/all', asyncHandler(async (req, res) => {
  const { month, employee, search } = req.query;

  const filters = {};
  if (month) filters.payrollMonth = month;
  if (employee) filters.name = employee;
  if (search) filters.search = search;

  const data = await SalaryModel.findAll(filters);
  const total = await SalaryModel.count(filters);

  res.json({
    success: true,
    data,
    total
  });
}));

// GET /api/salary - Get all salary records with filtering and pagination
router.get('/', validate(schemas.queryParams, 'query'), asyncHandler(async (req, res) => {
  const { page, limit, month, employee, search } = req.query;

  const filters = {};
  if (month) filters.payrollMonth = month;
  if (employee) filters.name = employee;
  if (search) filters.search = search;

  const offset = (page - 1) * limit;
  const pagination = { limit, offset };

  const data = await SalaryModel.findAll(filters, pagination);
  const total = await SalaryModel.count(filters);

  res.json({
    success: true,
    data,
    pagination: {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit)
    }
  });
}));

// GET /api/salary/:id - Get single salary record
router.get('/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;
  const data = await SalaryModel.findById(id);

  if (!data) {
    return res.status(404).json({
      success: false,
      message: 'Salary record not found'
    });
  }

  res.json({
    success: true,
    data
  });
}));

// POST /api/salary - Create new salary record
router.post('/', validate(schemas.salary), asyncHandler(async (req, res) => {
  const data = await SalaryModel.create(req.body);

  res.status(201).json({
    success: true,
    data,
    message: 'Salary record created successfully'
  });
}));

// POST /api/salary/bulk - Bulk create/update salary records
router.post('/bulk', validateBulk(schemas.salary), asyncHandler(async (req, res) => {
  const result = await SalaryModel.bulkUpsert(req.body);

  res.json({
    success: true,
    data: result,
    message: `Bulk operation completed: ${result.imported} imported, ${result.updated} updated`
  });
}));

// PUT /api/salary/:id - Update salary record
router.put('/:id', validate(schemas.salary), asyncHandler(async (req, res) => {
  const { id } = req.params;
  const data = await SalaryModel.update(id, req.body);

  res.json({
    success: true,
    data,
    message: 'Salary record updated successfully'
  });
}));

// DELETE /api/salary/:id - Delete single salary record
router.delete('/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;
  const result = await SalaryModel.delete(id);

  res.json({
    success: true,
    data: result,
    message: 'Salary record deleted successfully'
  });
}));

// DELETE /api/salary - Delete multiple salary records
router.delete('/', asyncHandler(async (req, res) => {
  const { ids } = req.body;

  if (!Array.isArray(ids) || ids.length === 0) {
    return res.status(400).json({
      success: false,
      message: 'IDs array is required'
    });
  }

  const result = await SalaryModel.deleteMany(ids);

  res.json({
    success: true,
    data: result,
    message: `${result.deletedCount} salary records deleted successfully`
  });
}));

// DELETE /api/salary/clear - Clear all salary data
router.delete('/clear', asyncHandler(async (req, res) => {
  const result = await SalaryModel.clearAll();

  res.json({
    success: true,
    data: result,
    message: 'All salary data cleared successfully'
  });
}));

// GET /api/salary/filters/months - Get unique payroll months
router.get('/filters/months', asyncHandler(async (req, res) => {
  const months = await SalaryModel.getUniqueMonths();

  res.json({
    success: true,
    data: months
  });
}));

// GET /api/salary/filters/employees - Get unique employees
router.get('/filters/employees', asyncHandler(async (req, res) => {
  const employees = await SalaryModel.getUniqueEmployees();

  res.json({
    success: true,
    data: employees
  });
}));

// GET /api/salary/summary/monthly - Get monthly salary summary
router.get('/summary/monthly', asyncHandler(async (req, res) => {
  const summary = await SalaryModel.getSalaryMonthlySummary();

  res.json({
    success: true,
    data: summary
  });
}));

// GET /api/salary/employee/:name/trend - Get salary trend for employee
router.get('/employee/:name/trend', asyncHandler(async (req, res) => {
  const { name } = req.params;
  const trend = await SalaryModel.getEmployeeSalaryTrend(name);

  res.json({
    success: true,
    data: trend
  });
}));

// GET /api/salary/top-earners/:month - Get top earners for a month
router.get('/top-earners/:month', asyncHandler(async (req, res) => {
  const { month } = req.params;
  const { limit = 10 } = req.query;
  const topEarners = await SalaryModel.getTopEarners(month, parseInt(limit));

  res.json({
    success: true,
    data: topEarners
  });
}));

module.exports = router;
