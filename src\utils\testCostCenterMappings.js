// Test file for cost center mapping utilities
import { getUniqueCostCenters, applyCostCenterMapping } from './costCenterMappings';

// Test data with case variations
const testData = [
  { costCenter: 'IT Department', name: '<PERSON>', month: 'Jan' },
  { costCenter: 'it department', name: '<PERSON>', month: 'Jan' },
  { costCenter: 'IT DEPARTMENT', name: '<PERSON>', month: 'Feb' },
  { costCenter: 'HR Department', name: '<PERSON>', month: 'Jan' },
  { costCenter: 'hr department', name: '<PERSON>', month: 'Feb' },
  { costCenter: 'Finance', name: '<PERSON>', month: 'Jan' },
  { costCenter: 'FINANCE', name: 'Eve', month: 'Feb' },
  { costCenter: 'Marketing', name: '<PERSON>', month: 'Jan' },
  { costCenter: 'marketing', name: '<PERSON>', month: 'Feb' },
  { costCenter: 'Operations', name: '<PERSON>', month: 'Jan' },
];

// Test getUniqueCostCenters
console.log('Testing getUniqueCostCenters...');
const uniqueCostCenters = getUniqueCostCenters(testData);
console.log('Expected: 5 unique cost centers');
console.log('Actual:', uniqueCostCenters);
console.log('Count:', uniqueCostCenters.length);

// Test applyCostCenterMapping
console.log('\nTesting applyCostCenterMapping...');
const mappings = {
  'IT Department': 'Information Technology',
  'HR Department': 'Human Resources'
};

console.log('Testing exact match:', applyCostCenterMapping('IT Department', mappings));
console.log('Testing case variation:', applyCostCenterMapping('it department', mappings));
console.log('Testing uppercase:', applyCostCenterMapping('IT DEPARTMENT', mappings));
console.log('Testing no mapping:', applyCostCenterMapping('Finance', mappings));

export { testData, uniqueCostCenters };
