@echo off
echo ========================================
echo   Financial Report Application
echo   Simple Server Startup (Separate Windows)
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js is available
echo.

REM Kill any existing processes on ports 3000 and 5000
echo 🔄 Stopping existing servers...
for /f "tokens=5" %%a in ('netstat -aon ^| find ":3000" ^| find "LISTENING"') do (
    echo Stopping process on port 3000 (PID: %%a)
    taskkill /f /pid %%a >nul 2>&1
)

for /f "tokens=5" %%a in ('netstat -aon ^| find ":5000" ^| find "LISTENING"') do (
    echo Stopping process on port 5000 (PID: %%a)
    taskkill /f /pid %%a >nul 2>&1
)

echo.
echo 🚀 Starting servers in separate windows...
echo.

REM Start backend server in new window
echo 🔧 Starting backend server...
start "Financial Report - Backend Server" cmd /k "cd server && echo Starting backend server on port 5000... && npm start"

REM Wait a moment for backend to start
echo ⏳ Waiting 5 seconds for backend to initialize...
timeout /t 5 /nobreak >nul

REM Start frontend server in new window  
echo 🔧 Starting frontend server...
start "Financial Report - Frontend Server" cmd /k "echo Starting frontend server on port 3000... && npm start"

echo.
echo ✅ Servers are starting in separate windows:
echo 📍 Backend Server: http://localhost:5000
echo 📍 Frontend Server: http://localhost:3000
echo.
echo 📝 Note: 
echo   - Two command windows will open for the servers
echo   - Close those windows to stop the servers
echo   - Wait for both servers to fully start before using the application
echo.
echo ========================================
echo   Server startup initiated
echo ========================================
pause
