// Test script to check cost center mappings functionality
const Database = require('../config/database');
const CostCenterMappingModel = require('../models/CostCenterMappingModel');

async function testCostCenterMappings() {
  console.log('Testing Cost Center Mappings...');
  
  try {
    // Initialize database
    console.log('1. Initializing database...');
    await Database.init();
    console.log('✓ Database initialized successfully');

    // Test table exists
    console.log('2. Testing if cost_center_mappings table exists...');
    const db = Database.getDatabase();
    
    await new Promise((resolve, reject) => {
      db.get("SELECT name FROM sqlite_master WHERE type='table' AND name='cost_center_mappings'", (err, row) => {
        if (err) {
          reject(err);
        } else if (row) {
          console.log('✓ cost_center_mappings table exists');
          resolve();
        } else {
          reject(new Error('cost_center_mappings table does not exist'));
        }
      });
    });

    // Test basic operations
    console.log('3. Testing basic model operations...');
    
    // Test findAll
    console.log('3a. Testing findAll...');
    const allMappings = await CostCenterMappingModel.findAll();
    console.log(`✓ findAll returned ${allMappings.data.length} mappings`);

    // Test getAllMappingsAsObject
    console.log('3b. Testing getAllMappingsAsObject...');
    const mappingsObject = await CostCenterMappingModel.getAllMappingsAsObject();
    console.log(`✓ getAllMappingsAsObject returned object with ${Object.keys(mappingsObject).length} keys`);

    // Test create a mapping
    console.log('3c. Testing create mapping...');
    const testMapping = {
      originalName: 'Test Department',
      alternateName: 'Testing Dept'
    };
    
    const created = await CostCenterMappingModel.create(testMapping);
    console.log(`✓ Created mapping with ID: ${created.id}`);

    // Test findByOriginalName
    console.log('3d. Testing findByOriginalName...');
    const found = await CostCenterMappingModel.findByOriginalName('Test Department');
    console.log(`✓ Found mapping: ${found ? found.alternateName : 'null'}`);

    // Test bulkUpsert
    console.log('3e. Testing bulkUpsert...');
    const bulkMappings = [
      { originalName: 'IT Department', alternateName: 'Information Technology' },
      { originalName: 'HR Department', alternateName: 'Human Resources' }
    ];
    
    const bulkResult = await CostCenterMappingModel.bulkUpsert(bulkMappings);
    console.log(`✓ Bulk upsert processed ${bulkResult.length} mappings`);

    // Test cleanup
    console.log('4. Cleaning up test data...');
    await CostCenterMappingModel.delete(created.id);
    console.log('✓ Test data cleaned up');

    console.log('\n🎉 All tests passed! Cost Center Mappings functionality is working correctly.');

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

// Run the test
testCostCenterMappings();
