const BaseModel = require('./BaseModel');

class PaginationStateModel extends BaseModel {
  constructor() {
    super('pagination_state');
  }

  getSearchableFields() {
    return ['component'];
  }

  getDefaultOrderBy() {
    return 'updatedAt DESC';
  }

  // Override create to handle upsert behavior for pagination state
  upsert(component, state) {
    return new Promise((resolve, reject) => {
      const key = `${component}_pagination`;

      const existingQuery = `SELECT * FROM ${this.tableName} WHERE key = ?`;
      this.db.get(existingQuery, [key], (err, existing) => {
        if (err) {
          reject(err);
          return;
        }

        const timestamp = new Date().toISOString();

        if (existing) {
          // Update existing record
          const updateQuery = `
            UPDATE ${this.tableName}
            SET currentPage = ?, itemsPerPage = ?, updatedAt = ?
            WHERE key = ?
          `;
          this.db.run(updateQuery, [
            state.currentPage || 1,
            state.itemsPerPage || 100,
            timestamp,
            key
          ], (err) => {
            if (err) {
              reject(err);
            } else {
              this.findByKey(key)
                .then(resolve)
                .catch(reject);
            }
          });
        } else {
          // Insert new record
          const insertQuery = `
            INSERT INTO ${this.tableName} (key, component, currentPage, itemsPerPage, updatedAt)
            VALUES (?, ?, ?, ?, ?)
          `;
          this.db.run(insertQuery, [
            key,
            component,
            state.currentPage || 1,
            state.itemsPerPage || 100,
            timestamp
          ], (err) => {
            if (err) {
              reject(err);
            } else {
              this.findByKey(key)
                .then(resolve)
                .catch(reject);
            }
          });
        }
      });
    });
  }

  // Find by key (component_pagination)
  findByKey(key) {
    return new Promise((resolve, reject) => {
      const query = `SELECT * FROM ${this.tableName} WHERE key = ?`;
      this.db.get(query, [key], (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row || null);
        }
      });
    });
  }

  // Find by component name
  findByComponent(component) {
    return new Promise((resolve, reject) => {
      const key = `${component}_pagination`;
      this.findByKey(key)
        .then(result => {
          if (result) {
            resolve({
              currentPage: result.currentPage || 1,
              itemsPerPage: result.itemsPerPage || 100
            });
          } else {
            // Return default values if not found
            resolve({
              currentPage: 1,
              itemsPerPage: 100
            });
          }
        })
        .catch(reject);
    });
  }

  // Get all pagination states
  getAllStates() {
    return new Promise((resolve, reject) => {
      const query = `SELECT * FROM ${this.tableName} ORDER BY component`;
      this.db.all(query, [], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows || []);
        }
      });
    });
  }

  // Delete by component
  deleteByComponent(component) {
    return new Promise((resolve, reject) => {
      const key = `${component}_pagination`;
      const query = `DELETE FROM ${this.tableName} WHERE key = ?`;
      this.db.run(query, [key], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ success: true, deletedCount: this.changes });
        }
      });
    });
  }

  // Reset all pagination states to defaults
  resetAll() {
    return new Promise((resolve, reject) => {
      const query = `
        UPDATE ${this.tableName}
        SET currentPage = 1, itemsPerPage = 100, updatedAt = ?
      `;
      this.db.run(query, [new Date().toISOString()], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ success: true, updatedCount: this.changes });
        }
      });
    });
  }
}

module.exports = new PaginationStateModel();
