const dbManager = require('../config/database');

async function simpleTest() {
  try {
    console.log('🔍 Simple database test...');
    
    // Initialize database
    await dbManager.init();
    console.log('✅ Database initialized');
    
    // Get database connection
    const db = dbManager.getDatabase();
    console.log('✅ Database connection obtained');
    
    // Test a simple query
    const result = await new Promise((resolve, reject) => {
      db.get('SELECT COUNT(*) as count FROM pagination_state', [], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });
    
    console.log('✅ Query successful:', result);
    
    // Test insert
    const insertResult = await new Promise((resolve, reject) => {
      db.run(
        'INSERT INTO pagination_state (key, component, currentPage, itemsPerPage, updatedAt) VALUES (?, ?, ?, ?, ?)',
        ['test_pagination', 'test', 1, 100, new Date().toISOString()],
        function(err) {
          if (err) reject(err);
          else resolve({ changes: this.changes, lastID: this.lastID });
        }
      );
    });
    
    console.log('✅ Insert successful:', insertResult);
    
    // Test select
    const selectResult = await new Promise((resolve, reject) => {
      db.get('SELECT * FROM pagination_state WHERE key = ?', ['test_pagination'], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });
    
    console.log('✅ Select successful:', selectResult);
    
    console.log('🎉 All tests passed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await dbManager.close();
    console.log('🔒 Database closed');
  }
}

simpleTest();
