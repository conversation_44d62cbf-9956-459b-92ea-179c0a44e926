@echo off
echo ========================================
echo   Financial Report Application
echo   Manual Server Startup
echo ========================================
echo.

echo 🔧 Starting Backend Server...
echo Opening new window for backend...
start "Financial Report - Backend" cmd /k "cd server && echo Backend starting on http://localhost:5000 && npm start"

echo.
echo ⏳ Waiting 5 seconds for backend to start...
timeout /t 5 /nobreak >nul

echo 🔧 Starting Frontend Server...
echo Opening new window for frontend...
start "Financial Report - Frontend" cmd /k "echo Frontend starting on http://localhost:3000 && npm start"

echo.
echo ✅ Both servers are starting in separate windows:
echo 📍 Backend: http://localhost:5000
echo 📍 Frontend: http://localhost:3000
echo.
echo 📝 Instructions:
echo   1. Wait for both servers to fully start (may take 1-2 minutes)
echo   2. Open http://localhost:3000 in your browser
echo   3. Close the server windows when done
echo.
echo ========================================
echo   Manual startup completed
echo ========================================
pause
