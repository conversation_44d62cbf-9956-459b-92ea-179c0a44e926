const dbManager = require('../config/database');

class BaseModel {
  constructor(tableName) {
    this.tableName = tableName;
  }

  // Lazy getter for database connection
  get db() {
    return dbManager.getDatabase();
  }

  // Generic CRUD operations
  findAll(filters = {}, pagination = {}) {
    return new Promise((resolve, reject) => {
      let query = `SELECT * FROM ${this.tableName}`;
      const params = [];
      const conditions = [];

      // Apply filters
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          if (key === 'search') {
            // Generic search across text fields
            const searchConditions = this.getSearchableFields().map(field =>
              `${field} LIKE ?`
            ).join(' OR ');
            if (searchConditions) {
              conditions.push(`(${searchConditions})`);
              this.getSearchableFields().forEach(() => params.push(`%${value}%`));
            }
          } else {
            conditions.push(`${key} = ?`);
            params.push(value);
          }
        }
      });

      if (conditions.length > 0) {
        query += ` WHERE ${conditions.join(' AND ')}`;
      }

      // Add ordering
      query += ` ORDER BY ${this.getDefaultOrderBy()}`;

      // Add pagination
      if (pagination.limit) {
        query += ` LIMIT ?`;
        params.push(pagination.limit);

        if (pagination.offset) {
          query += ` OFFSET ?`;
          params.push(pagination.offset);
        }
      }

      this.db.all(query, params, (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows || []);
        }
      });
    });
  }

  findById(id) {
    return new Promise((resolve, reject) => {
      const query = `SELECT * FROM ${this.tableName} WHERE id = ?`;
      this.db.get(query, [id], (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row || null);
        }
      });
    });
  }

  count(filters = {}) {
    return new Promise((resolve, reject) => {
      let query = `SELECT COUNT(*) as count FROM ${this.tableName}`;
      const params = [];
      const conditions = [];

      // Apply same filters as findAll
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          if (key === 'search') {
            const searchConditions = this.getSearchableFields().map(field =>
              `${field} LIKE ?`
            ).join(' OR ');
            if (searchConditions) {
              conditions.push(`(${searchConditions})`);
              this.getSearchableFields().forEach(() => params.push(`%${value}%`));
            }
          } else {
            conditions.push(`${key} = ?`);
            params.push(value);
          }
        }
      });

      if (conditions.length > 0) {
        query += ` WHERE ${conditions.join(' AND ')}`;
      }

      this.db.get(query, params, (err, result) => {
        if (err) {
          reject(err);
        } else {
          resolve(result ? result.count : 0);
        }
      });
    });
  }

  create(data) {
    return new Promise((resolve, reject) => {
      const fields = Object.keys(data);
      const values = Object.values(data);

      // Add timestamps
      if (!data.createdAt) {
        fields.push('createdAt');
        values.push(new Date().toISOString());
      }
      if (!data.updatedAt) {
        fields.push('updatedAt');
        values.push(new Date().toISOString());
      }

      const placeholders = fields.map(() => '?').join(', ');
      const query = `INSERT INTO ${this.tableName} (${fields.join(', ')}) VALUES (${placeholders})`;

      this.db.run(query, values, function(err) {
        if (err) {
          reject(err);
        } else {
          // Use this.lastID to get the inserted row ID
          resolve({ id: this.lastID, ...data });
        }
      });
    });
  }

  update(id, data) {
    return new Promise((resolve, reject) => {
      const fields = Object.keys(data);
      const setClause = fields.map(field => `${field} = ?`).join(', ');
      const values = [...Object.values(data), new Date().toISOString(), id];

      const query = `UPDATE ${this.tableName} SET ${setClause}, updatedAt = ? WHERE id = ?`;

      this.db.run(query, values, (err) => {
        if (err) {
          reject(err);
        } else {
          // Get the updated record
          this.findById(id)
            .then(updatedRecord => {
              if (!updatedRecord) {
                reject(new Error(`Record with id ${id} not found`));
              } else {
                resolve(updatedRecord);
              }
            })
            .catch(reject);
        }
      });
    });
  }

  delete(id) {
    return new Promise((resolve, reject) => {
      const query = `DELETE FROM ${this.tableName} WHERE id = ?`;

      this.db.run(query, [id], function(err) {
        if (err) {
          reject(err);
        } else if (this.changes === 0) {
          reject(new Error(`Record with id ${id} not found`));
        } else {
          resolve({ success: true, deletedId: id });
        }
      });
    });
  }

  deleteMany(ids) {
    return new Promise((resolve, reject) => {
      if (!Array.isArray(ids) || ids.length === 0) {
        reject(new Error('IDs array is required'));
        return;
      }

      const placeholders = ids.map(() => '?').join(', ');
      const query = `DELETE FROM ${this.tableName} WHERE id IN (${placeholders})`;

      this.db.run(query, ids, function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ success: true, deletedCount: this.changes });
        }
      });
    });
  }

  bulkCreate(dataArray) {
    return new Promise((resolve, reject) => {
      if (!Array.isArray(dataArray) || dataArray.length === 0) {
        resolve([]);
        return;
      }

      this.db.serialize(() => {
        this.db.run('BEGIN TRANSACTION');

        const results = [];
        let completed = 0;
        let hasError = false;

        const processItem = (item, index) => {
          this.create(item)
            .then(result => {
              if (!hasError) {
                results[index] = result;
                completed++;

                if (completed === dataArray.length) {
                  this.db.run('COMMIT', (err) => {
                    if (err) {
                      reject(err);
                    } else {
                      resolve(results);
                    }
                  });
                }
              }
            })
            .catch(error => {
              if (!hasError) {
                hasError = true;
                this.db.run('ROLLBACK', () => {
                  reject(error);
                });
              }
            });
        };

        dataArray.forEach(processItem);
      });
    });
  }

  clearAll() {
    return new Promise((resolve, reject) => {
      const query = `DELETE FROM ${this.tableName}`;

      this.db.run(query, function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ success: true, deletedCount: this.changes });
        }
      });
    });
  }

  // Override these methods in child classes
  getSearchableFields() {
    return ['name']; // Default searchable field
  }

  getDefaultOrderBy() {
    return 'id DESC'; // Default ordering
  }
}

module.exports = BaseModel;
