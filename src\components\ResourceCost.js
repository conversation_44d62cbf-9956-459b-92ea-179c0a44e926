// src/components/ResourceCost.js
import React, { useState, useEffect } from "react";
import {
  Container,
  Typography,
  Box,
  Button,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  TableContainer,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Checkbox,
  Card,
  CardContent,
  IconButton,
  Tooltip,
  useTheme,
  TablePagination
} from "@mui/material";
import Autocomplete from "@mui/material/Autocomplete";
import ListItemText from "@mui/material/ListItemText";
import * as XLSX from "xlsx";
import { loadCostCenterData, loadSalaryData } from "../db";
import { motion } from "framer-motion";
import MonetizationOnIcon from "@mui/icons-material/MonetizationOn";
import FileDownloadIcon from "@mui/icons-material/FileDownload";
import DeleteIcon from "@mui/icons-material/Delete";
import FilterListIcon from "@mui/icons-material/FilterList";
import ClearAllIcon from "@mui/icons-material/ClearAll";
import SearchIcon from "@mui/icons-material/Search";

// Import custom components
import PageHeader from "./common/PageHeader";
import TableSkeleton from "./common/TableSkeleton";
import NoDataPlaceholder from "./common/NoDataPlaceholder";
import { useToast } from "./common/ToastProvider";
import AnimationWrapper from "./common/AnimationWrapper";

const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MENU_PROPS = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 300,
    },
  },
};

// Helper to normalize strings (trim and lowercase)
const normalize = (str) => (str ? str.trim().toLowerCase() : "");

const ResourceCost = () => {
  const theme = useTheme();
  const { showToast } = useToast();

  // Filter states
  const [selectedMonth, setSelectedMonth] = useState("");
  const [selectedEmployees, setSelectedEmployees] = useState([]);
  const [selectedCostCenters, setSelectedCostCenters] = useState([]);
  const [universalSearch, setUniversalSearch] = useState("");

  // Unique options from cost center data
  const [uniqueMonths, setUniqueMonths] = useState([]);
  const [uniqueEmployees, setUniqueEmployees] = useState([]);
  const [uniqueCostCenters, setUniqueCostCenters] = useState([]);

  // Computed cost data: each record has:
  // { employee, totalHours, hoursByCostCenter: { [cc]: hours },
  //   salary: { gross, pf }, cost: { [cc]: { gross, pf } } }
  const [computedData, setComputedData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedRows, setSelectedRows] = useState({}); // keyed by employee name

  // Pagination
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(100);

  // Fetch unique filter options from cost center data
  useEffect(() => {
    const fetchUniqueValues = async () => {
      const ccData = await loadCostCenterData();
      const months = ccData
        .filter((r) => r.month && r.month.trim() !== "")
        .map((r) => r.month.trim());
      setUniqueMonths([...new Set(months)]);
      const empNames = ccData
        .filter((r) => r.name && r.name.trim() !== "")
        .map((r) => r.name.trim());
      setUniqueEmployees([...new Set(empNames)]);
      const costCenters = ccData
        .filter((r) => r.costCenter && r.costCenter.trim() !== "")
        .map((r) => r.costCenter.trim());
      setUniqueCostCenters([...new Set(costCenters)]);
    };
    fetchUniqueValues();

    // Listen for database changes to refresh unique values
    const handleDBChange = () => fetchUniqueValues();
    window.addEventListener("dbChange", handleDBChange);
    return () => window.removeEventListener("dbChange", handleDBChange);
  }, []);

  // Compute cost data based on selected month
  useEffect(() => {
    if (!selectedMonth) {
      setComputedData([]);
      return;
    }
    const computeCosts = async () => {
      setLoading(true);
      const ccData = await loadCostCenterData();
      const salaryData = await loadSalaryData();

      // Filter cost center records for the selected month
      const filteredCC = ccData.filter(
        (r) => r.month && r.month.trim() === selectedMonth
      );

      // Group records by employee
      const groups = {};
      filteredCC.forEach((r) => {
        const emp = r.name ? r.name.trim() : "";
        if (!emp) return;
        if (!groups[emp]) groups[emp] = [];
        groups[emp].push(r);
      });

      // For each employee, compute:
      // 1. Total hours (using hoursWorked if available, else hours)
      // 2. Hours booked per cost center
      // 3. Then compute cost per cost center: (salary / totalHours) * hours booked in that cost center
      const computed = Object.keys(groups).map((emp) => {
        const records = groups[emp];
        const totalHours = records.reduce((sum, r) => {
          const hrs = r.hoursWorked
            ? parseFloat(r.hoursWorked)
            : parseFloat(r.hours) || 0;
          return sum + hrs;
        }, 0);
        const hoursByCostCenter = {};
        records.forEach((r) => {
          const cc = r.costCenter ? r.costCenter.trim() : "";
          const hrs = r.hoursWorked
            ? parseFloat(r.hoursWorked)
            : parseFloat(r.hours) || 0;
          if (cc) {
            hoursByCostCenter[cc] = (hoursByCostCenter[cc] || 0) + hrs;
          }
        });
        // Retrieve salary data for the employee, filtering by the selected month (assumed to be in s.payrollMonth)
        const salaryRecord = salaryData.find(
          (s) =>
            s.name &&
            normalize(s.name) === normalize(emp) &&
            s.payrollMonth &&
            s.payrollMonth.trim() === selectedMonth
        );
        const salary = salaryRecord
          ? {
              gross: salaryRecord.gross ? parseFloat(salaryRecord.gross) : 0,
              pf: salaryRecord.pf ? parseFloat(salaryRecord.pf) : 0,
            }
          : { gross: 0, pf: 0 };

        const cost = {};
        Object.keys(hoursByCostCenter).forEach((cc) => {
          const hrsInCC = hoursByCostCenter[cc];
          cost[cc] = {
            gross: totalHours > 0 ? (salary.gross / totalHours) * hrsInCC : 0,
            pf: totalHours > 0 ? (salary.pf / totalHours) * hrsInCC : 0,
          };
        });

        return {
          employee: emp,
          totalHours,
          hoursByCostCenter,
          salary,
          cost,
        };
      });
      setComputedData(computed);
      setLoading(false);
    };
    computeCosts();

    // Listen for database changes to recompute costs
    const handleDBChange = () => {
      if (selectedMonth) computeCosts();
    };
    window.addEventListener("dbChange", handleDBChange);
    return () => window.removeEventListener("dbChange", handleDBChange);
  }, [selectedMonth]);

  // Apply universal search and filters
  let filteredData = computedData.filter((rec) => {
    const query = universalSearch.toLowerCase();
    const empMatch = rec.employee.toLowerCase().includes(query);
    const ccMatch = Object.keys(rec.hoursByCostCenter || {}).some((cc) =>
      cc.toLowerCase().includes(query)
    );
    let base = empMatch || ccMatch;
    if (selectedEmployees.length > 0) {
      base =
        base &&
        selectedEmployees.map(normalize).includes(rec.employee.toLowerCase());
    }
    return base;
  });
  if (selectedCostCenters.length > 0) {
    filteredData = filteredData.filter((rec) =>
      selectedCostCenters.some((cc) => (rec.hoursByCostCenter?.[cc] || 0) > 0)
    );
  }

  // Determine cost center columns to display: if filter applied, use those; else, use all unique cost centers.
  const baseColumns =
    selectedCostCenters.length > 0 ? selectedCostCenters : uniqueCostCenters;
  const displayColumns = baseColumns.filter((cc) =>
    filteredData.some((rec) => (rec.hoursByCostCenter?.[cc] || 0) > 0)
  );

  // Build final data: For each employee, compute total gross cost across displayed cost centers.
  const finalData = filteredData.map((rec) => {
    const totalGross = displayColumns.reduce(
      (sum, cc) => sum + ((rec.cost && rec.cost[cc]?.gross) || 0),
      0
    );
    return { ...rec, totalGross };
  });

  // Derive arrays for Gross and PF tables
  const grossData = finalData.map((rec) => {
    let total = 0;
    const costs = {};
    displayColumns.forEach((cc) => {
      const val = (rec.cost && rec.cost[cc]?.gross) || 0;
      costs[cc] = val;
      total += val;
    });
    return { employee: rec.employee, costs, total };
  });

  const pfData = finalData.map((rec) => {
    let total = 0;
    const costs = {};
    displayColumns.forEach((cc) => {
      const val = (rec.cost && rec.cost[cc]?.pf) || 0;
      costs[cc] = val;
      total += val;
    });
    return { employee: rec.employee, costs, total };
  });

  // Row selection handlers
  const handleSelectEntry = (employee) => {
    setSelectedRows((prev) => ({ ...prev, [employee]: !prev[employee] }));
  };

  const handleSelectAll = (checked) => {
    // Start with current selections
    const newSelections = { ...selectedRows };

    // Update selections for visible rows only (current page)
    const currentPageData = paginatedGrossData.map(row => row.employee);
    currentPageData.forEach(employee => {
      newSelections[employee] = checked;
    });

    setSelectedRows(newSelections);
  };
  // Handle pagination
  const handleChangePage = (_, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Apply pagination to the data
  const paginatedGrossData = grossData.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);
  const paginatedPfData = pfData.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);

  const handleDeleteSelected = () => {
    const selectedCount = Object.values(selectedRows).filter(Boolean).length;
    if (selectedCount === 0) {
      showToast('Please select at least one row to delete', 'warning');
      return;
    }

    const remaining = computedData.filter((rec) => !selectedRows[rec.employee]);
    setComputedData(remaining);
    setSelectedRows({});

    showToast(`Successfully deleted ${selectedCount} item${selectedCount !== 1 ? 's' : ''}`, 'success');
  };

  // Export to Excel: Two sheets (Gross and PF)
  const handleExport = () => {
    if (finalData.length === 0) {
      showToast('No data to export', 'warning');
      return;
    }

    const wb = XLSX.utils.book_new();

    // Gross Sheet
    const grossHeader = ["Employee", ...displayColumns.map((cc) => cc + " Gross"), "Total Gross"];
    const grossRows = grossData.map((row) => {
      const r = [row.employee];
      displayColumns.forEach((cc) => {
        r.push(row.costs[cc] > 0 ? row.costs[cc].toFixed(2) : "");
      });
      r.push(row.total.toFixed(2));
      return r;
    });
    const wsGross = XLSX.utils.aoa_to_sheet([grossHeader, ...grossRows]);
    XLSX.utils.book_append_sheet(wb, wsGross, "Gross Cost");

    // PF Sheet
    const pfHeader = ["Employee", ...displayColumns.map((cc) => cc + " PF"), "Total PF"];
    const pfRows = pfData.map((row) => {
      const r = [row.employee];
      displayColumns.forEach((cc) => {
        r.push(row.costs[cc] > 0 ? row.costs[cc].toFixed(2) : "");
      });
      r.push(row.total.toFixed(2));
      return r;
    });
    const wsPF = XLSX.utils.aoa_to_sheet([pfHeader, ...pfRows]);
    XLSX.utils.book_append_sheet(wb, wsPF, "PF Cost");

    XLSX.writeFile(wb, `ResourceCost_${selectedMonth}.xlsx`);
    showToast('Data exported successfully', 'success');
  };

  const handleClearFilters = () => {
    setSelectedEmployees([]);
    setSelectedCostCenters([]);
    setUniversalSearch("");
    showToast('Filters cleared', 'info');
  };

  // Define page actions for the header
  const pageActions = [
    {
      label: 'Export to Excel',
      onClick: handleExport,
      icon: <FileDownloadIcon />,
      variant: 'contained',
      color: 'primary',
    },
    {
      label: 'Delete Selected',
      onClick: handleDeleteSelected,
      icon: <DeleteIcon />,
      variant: 'contained',
      color: 'error',
      disabled: Object.values(selectedRows).filter(Boolean).length === 0,
    },
  ];

  return (
    <AnimationWrapper type="fadeIn">
      <Container maxWidth="xl" sx={{ py: 3 }}>
        {/* Page Header */}
        <PageHeader
          title="Resource Cost"
          subtitle="View and analyze resource costs across different cost centers"
          icon={MonetizationOnIcon}
          actions={pageActions}
        />
      {/* Filters Section */}
      <Card sx={{ mb: 3, p: 2 }}>
        <CardContent>
          <Box sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
            <FilterListIcon sx={{ mr: 1, color: theme.palette.primary.main }} />
            <Typography variant="h6">Filters</Typography>
          </Box>

          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
            {/* Month Selector */}
            <FormControl sx={{ minWidth: 200 }}>
              <InputLabel id="rc-month-label">Select Month</InputLabel>
              <Select
                labelId="rc-month-label"
                value={selectedMonth}
                label="Select Month"
                onChange={(e) => setSelectedMonth(e.target.value)}
              >
                {uniqueMonths.map((m, idx) => (
                  <MenuItem key={idx} value={m}>
                    {m}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            {/* Universal Search */}
            <Box>
              <TextField
                label="Search"
                variant="outlined"
                fullWidth
                value={universalSearch}
                onChange={(e) => setUniversalSearch(e.target.value)}
                placeholder="Search by employee or cost center..."
                InputProps={{
                  startAdornment: <SearchIcon sx={{ color: 'action.active', mr: 1 }} />,
                  endAdornment: universalSearch ? (
                    <IconButton
                      size="small"
                      onClick={() => setUniversalSearch('')}
                      edge="end"
                    >
                      <ClearAllIcon fontSize="small" />
                    </IconButton>
                  ) : null,
                }}
              />
            </Box>

            {/* Employee Filter */}
            <Box>
              <Autocomplete
                multiple
                options={uniqueEmployees}
                value={selectedEmployees}
                onChange={(_, newValue) => setSelectedEmployees(newValue)}
                disableCloseOnSelect
                renderOption={(props, option, { selected }) => (
                  <motion.li
                    {...props}
                    whileHover={{ backgroundColor: theme.palette.action.hover }}
                    transition={{ duration: 0.1 }}
                  >
                    <Checkbox checked={selected} style={{ marginRight: 8 }} />
                    <ListItemText primary={option} />
                  </motion.li>
                )}
                renderInput={(params) => (
                  <TextField {...params} label="Filter Employees" placeholder="Employees" />
                )}
                fullWidth
                MenuProps={MENU_PROPS}
              />
            </Box>

            {/* Cost Center Filter */}
            <Box>
              <Autocomplete
                multiple
                options={uniqueCostCenters}
                value={selectedCostCenters}
                onChange={(_, newValue) => setSelectedCostCenters(newValue)}
                disableCloseOnSelect
                renderOption={(props, option, { selected }) => (
                  <motion.li
                    {...props}
                    whileHover={{ backgroundColor: theme.palette.action.hover }}
                    transition={{ duration: 0.1 }}
                  >
                    <Checkbox checked={selected} style={{ marginRight: 8 }} />
                    <ListItemText primary={option} />
                  </motion.li>
                )}
                renderInput={(params) => (
                  <TextField {...params} label="Filter Cost Centers" placeholder="Cost Centers" />
                )}
                fullWidth
                MenuProps={MENU_PROPS}
              />
            </Box>

            {/* Clear Filters Button */}
            <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
              <Button
                variant="outlined"
                onClick={handleClearFilters}
                startIcon={<ClearAllIcon />}
              >
                Clear All Filters
              </Button>
            </Box>
          </Box>
        </CardContent>
      </Card>
      {loading ? (
        <TableSkeleton
          rows={5}
          columns={uniqueCostCenters.length + 2}
          showTableHeader
        />
      ) : finalData.length === 0 ? (
        <NoDataPlaceholder
          type={!selectedMonth ? 'empty' : 'noResults'}
          title={!selectedMonth ? 'Select a Month' : 'No Data Found'}
          description={!selectedMonth ? 'Please select a month to view resource costs' : 'Try adjusting your filters to find what you\'re looking for'}
          primaryAction={{
            label: !selectedMonth ? 'Select Month' : 'Clear Filters',
            onClick: !selectedMonth ? () => document.getElementById('rc-month-label').focus() : handleClearFilters,
          }}
        />
      ) : (
        <>
          {/* Gross Cost Table */}
          <Card sx={{ mb: 4, overflow: 'hidden' }}>
            <Box sx={{ p: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center', borderBottom: `1px solid ${theme.palette.divider}` }}>
              <Typography variant="h6" sx={{ fontWeight: 600, display: 'flex', alignItems: 'center' }}>
                <MonetizationOnIcon sx={{ mr: 1, color: theme.palette.primary.main }} />
                Gross Cost
              </Typography>
              <Typography variant="caption" color="text.secondary" sx={{ display: 'flex', alignItems: 'center' }}>
                <span style={{ marginRight: '4px' }}>Scroll horizontally to view all columns</span>
                <span style={{ fontSize: '16px' }}>↔️</span>
              </Typography>
            </Box>
            <CardContent sx={{ p: 0 }}>
              <TableContainer sx={{ maxWidth: '100%', overflowX: 'auto', height: '400px' }}>
                <Table stickyHeader size="small" sx={{ minWidth: uniqueCostCenters.length > 5 ? (uniqueCostCenters.length * 150) + 300 : '100%' }}>
                  <TableHead>
                    <TableRow>
                      <TableCell padding="checkbox" sx={{
                        position: 'sticky',
                        left: 0,
                        top: 0,
                        zIndex: 4,
                        backgroundColor: theme.palette.background.default,
                        borderBottom: `2px solid ${theme.palette.primary.main}`,
                      }}>
                        <Tooltip title="Select All Visible Rows">
                          <Checkbox
                            checked={paginatedGrossData.length > 0 && paginatedGrossData.every(row => selectedRows[row.employee])}
                            onChange={(e) => handleSelectAll(e.target.checked)}
                            indeterminate={
                              paginatedGrossData.some(row => selectedRows[row.employee]) &&
                              !paginatedGrossData.every(row => selectedRows[row.employee])
                            }
                          />
                        </Tooltip>
                      </TableCell>
                      <TableCell sx={{
                        fontWeight: "bold",
                        position: 'sticky',
                        left: 57,
                        top: 0,
                        zIndex: 4,
                        backgroundColor: theme.palette.background.default,
                        borderBottom: `2px solid ${theme.palette.primary.main}`,
                        boxShadow: '2px 0 4px -2px rgba(0,0,0,0.15)'
                      }}>Employee</TableCell>
                      {uniqueCostCenters.map((cc, idx) => (
                        <TableCell key={idx} sx={{
                          fontWeight: "bold",
                          position: 'sticky',
                          top: 0,
                          zIndex: 3,
                          backgroundColor: theme.palette.background.default,
                          borderBottom: `2px solid ${theme.palette.primary.main}`,
                        }}>
                          <Tooltip title={`Gross cost for ${cc}`} placement="top">
                            <span>{cc} Gross</span>
                          </Tooltip>
                        </TableCell>
                      ))}
                      <TableCell sx={{
                        fontWeight: "bold",
                        position: 'sticky',
                        top: 0,
                        zIndex: 3,
                        backgroundColor: theme.palette.background.default,
                        borderBottom: `2px solid ${theme.palette.primary.main}`,
                      }}>Total Gross</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {paginatedGrossData.map((row, index) => (
                      <TableRow
                        key={row.employee}
                        hover
                        sx={{
                          backgroundColor: index % 2 === 0 ? 'inherit' : theme.palette.grey[50],
                        }}
                      >
                        <TableCell padding="checkbox" sx={{ position: 'sticky', left: 0, zIndex: 2, backgroundColor: index % 2 === 0 ? theme.palette.background.paper : theme.palette.grey[50] }}>
                          <Checkbox
                            checked={!!selectedRows[row.employee]}
                            onChange={() => handleSelectEntry(row.employee)}
                          />
                        </TableCell>
                        <TableCell sx={{ position: 'sticky', left: 57, zIndex: 2, backgroundColor: index % 2 === 0 ? theme.palette.background.paper : theme.palette.grey[50], boxShadow: '2px 0 4px -2px rgba(0,0,0,0.15)' }}>
                          {row.employee}
                        </TableCell>
                        {uniqueCostCenters.map((cc, i) => (
                          <TableCell key={i}>
                            {row.costs[cc] > 0 ? row.costs[cc].toFixed(2) : ""}
                          </TableCell>
                        ))}
                        <TableCell sx={{ fontWeight: 'medium' }}>
                          {row.total.toFixed(2)}
                        </TableCell>
                      </TableRow>
                    ))}
                    <TableRow sx={{
                      backgroundColor: theme.palette.primary.light + '20',
                      '& td': { fontWeight: 'bold' }
                    }}>
                      <TableCell padding="checkbox" sx={{ position: 'sticky', left: 0, zIndex: 2, backgroundColor: theme.palette.primary.light + '20' }} />
                      <TableCell sx={{ position: 'sticky', left: 57, zIndex: 2, backgroundColor: theme.palette.primary.light + '20', boxShadow: '2px 0 4px -2px rgba(0,0,0,0.15)' }}>Total</TableCell>
                      {uniqueCostCenters.map((cc, i) => {
                        const total = finalData.reduce((sum, row) => sum + ((row.cost && row.cost[cc]?.gross) || 0), 0);
                        return (
                          <TableCell key={i}>
                            {total.toFixed(2)}
                          </TableCell>
                        );
                      })}
                      <TableCell>
                        {grossData.reduce((sum, row) => sum + row.total, 0).toFixed(2)}
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
              <TablePagination
                rowsPerPageOptions={[25, 50, 100, 200]}
                component="div"
                count={grossData.length}
                rowsPerPage={rowsPerPage}
                page={page}
                onPageChange={handleChangePage}
                onRowsPerPageChange={handleChangeRowsPerPage}
                sx={{ borderTop: `1px solid ${theme.palette.divider}` }}
              />
            </CardContent>
          </Card>
          {/* PF Cost Table */}
          <Card sx={{ overflow: 'hidden' }}>
            <Box sx={{ p: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center', borderBottom: `1px solid ${theme.palette.divider}` }}>
              <Typography variant="h6" sx={{ fontWeight: 600, display: 'flex', alignItems: 'center' }}>
                <MonetizationOnIcon sx={{ mr: 1, color: theme.palette.primary.main }} />
                PF Cost
              </Typography>
              <Typography variant="caption" color="text.secondary" sx={{ display: 'flex', alignItems: 'center' }}>
                <span style={{ marginRight: '4px' }}>Scroll horizontally to view all columns</span>
                <span style={{ fontSize: '16px' }}>↔️</span>
              </Typography>
            </Box>
            <CardContent sx={{ p: 0 }}>
              <TableContainer sx={{ maxWidth: '100%', overflowX: 'auto', height: '400px' }}>
                <Table stickyHeader size="small" sx={{ minWidth: uniqueCostCenters.length > 5 ? (uniqueCostCenters.length * 150) + 300 : '100%' }}>
                  <TableHead>
                    <TableRow>
                      <TableCell padding="checkbox" sx={{
                        position: 'sticky',
                        left: 0,
                        top: 0,
                        zIndex: 4,
                        backgroundColor: theme.palette.background.default,
                        borderBottom: `2px solid ${theme.palette.primary.main}`,
                      }}>
                        <Tooltip title="Select All Visible Rows">
                          <Checkbox
                            checked={paginatedPfData.length > 0 && paginatedPfData.every(row => selectedRows[row.employee])}
                            onChange={(e) => handleSelectAll(e.target.checked)}
                            indeterminate={
                              paginatedPfData.some(row => selectedRows[row.employee]) &&
                              !paginatedPfData.every(row => selectedRows[row.employee])
                            }
                          />
                        </Tooltip>
                      </TableCell>
                      <TableCell sx={{
                        fontWeight: "bold",
                        position: 'sticky',
                        left: 57,
                        top: 0,
                        zIndex: 4,
                        backgroundColor: theme.palette.background.default,
                        borderBottom: `2px solid ${theme.palette.primary.main}`,
                        boxShadow: '2px 0 4px -2px rgba(0,0,0,0.15)'
                      }}>Employee</TableCell>
                      {uniqueCostCenters.map((cc, idx) => (
                        <TableCell key={idx} sx={{
                          fontWeight: "bold",
                          position: 'sticky',
                          top: 0,
                          zIndex: 3,
                          backgroundColor: theme.palette.background.default,
                          borderBottom: `2px solid ${theme.palette.primary.main}`,
                        }}>
                          <Tooltip title={`PF cost for ${cc}`} placement="top">
                            <span>{cc} PF</span>
                          </Tooltip>
                        </TableCell>
                      ))}
                      <TableCell sx={{
                        fontWeight: "bold",
                        position: 'sticky',
                        top: 0,
                        zIndex: 3,
                        backgroundColor: theme.palette.background.default,
                        borderBottom: `2px solid ${theme.palette.primary.main}`,
                      }}>Total PF</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {paginatedPfData.map((row, index) => (
                      <TableRow
                        key={row.employee}
                        hover
                        sx={{
                          backgroundColor: index % 2 === 0 ? 'inherit' : theme.palette.grey[50],
                        }}
                      >
                        <TableCell padding="checkbox" sx={{ position: 'sticky', left: 0, zIndex: 2, backgroundColor: index % 2 === 0 ? theme.palette.background.paper : theme.palette.grey[50] }}>
                          <Checkbox
                            checked={!!selectedRows[row.employee]}
                            onChange={() => handleSelectEntry(row.employee)}
                          />
                        </TableCell>
                        <TableCell sx={{ position: 'sticky', left: 57, zIndex: 2, backgroundColor: index % 2 === 0 ? theme.palette.background.paper : theme.palette.grey[50], boxShadow: '2px 0 4px -2px rgba(0,0,0,0.15)' }}>
                          {row.employee}
                        </TableCell>
                        {uniqueCostCenters.map((cc, i) => (
                          <TableCell key={i}>
                            {row.costs[cc] > 0 ? row.costs[cc].toFixed(2) : ""}
                          </TableCell>
                        ))}
                        <TableCell sx={{ fontWeight: 'medium' }}>
                          {row.total.toFixed(2)}
                        </TableCell>
                      </TableRow>
                    ))}
                    <TableRow sx={{
                      backgroundColor: theme.palette.primary.light + '20',
                      '& td': { fontWeight: 'bold' }
                    }}>
                      <TableCell padding="checkbox" sx={{ position: 'sticky', left: 0, zIndex: 2, backgroundColor: theme.palette.primary.light + '20' }} />
                      <TableCell sx={{ position: 'sticky', left: 57, zIndex: 2, backgroundColor: theme.palette.primary.light + '20', boxShadow: '2px 0 4px -2px rgba(0,0,0,0.15)' }}>Total</TableCell>
                      {uniqueCostCenters.map((cc, i) => {
                        const total = finalData.reduce((sum, row) => sum + ((row.cost && row.cost[cc]?.pf) || 0), 0);
                        return (
                          <TableCell key={i}>
                            {total.toFixed(2)}
                          </TableCell>
                        );
                      })}
                      <TableCell>
                        {pfData.reduce((sum, row) => sum + row.total, 0).toFixed(2)}
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
              <TablePagination
                rowsPerPageOptions={[25, 50, 100, 200]}
                component="div"
                count={pfData.length}
                rowsPerPage={rowsPerPage}
                page={page}
                onPageChange={handleChangePage}
                onRowsPerPageChange={handleChangeRowsPerPage}
                sx={{ borderTop: `1px solid ${theme.palette.divider}` }}
              />
            </CardContent>
          </Card>
        </>
      )}
    </Container>
    </AnimationWrapper>
  );
};

export default ResourceCost;
